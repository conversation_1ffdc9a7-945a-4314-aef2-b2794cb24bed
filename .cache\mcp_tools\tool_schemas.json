{"version": "1.0", "updated_at": "2025-06-10T13:07:41.865824+00:00", "tools": [{"name": "search_across_modules", "description": "\nSearch across multiple Intacct modules.\n\nArgs:\n    query: Search query string\n    modules: List of modules to search (None = all enabled)\n    limit: Maximum results per module\n    \nReturns:\n    Search results organized by module\n", "parameters": {"properties": {"query": {"title": "Query", "type": "string"}, "modules": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "<PERSON><PERSON><PERSON>"}, "limit": {"default": 20, "title": "Limit", "type": "integer"}}, "required": ["query"], "type": "object"}, "server_name": "sage-intacct", "last_updated": "2025-06-10 13:07:39.065400+00:00"}, {"name": "get_financial_summary", "description": "\nGet a financial summary across modules.\n\nArgs:\n    start_date: Start date (YYYY-MM-DD)\n    end_date: End date (YYYY-MM-DD)\n    include_modules: Modules to include (default: all)\n    \nReturns:\n    Financial summary data\n", "parameters": {"properties": {"start_date": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Start Date"}, "end_date": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "End Date"}, "include_modules": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Include Mo<PERSON>les"}}, "type": "object"}, "server_name": "sage-intacct", "last_updated": "2025-06-10 13:07:39.065423+00:00"}, {"name": "execute_month_end_close", "description": "\nExecute month-end close procedures across modules.\n\nArgs:\n    period: Period to close (YYYY-MM)\n    modules: Modules to include (default: all)\n    dry_run: If True, simulate without making changes\n    \nReturns:\n    Month-end close results\n", "parameters": {"properties": {"period": {"title": "Period", "type": "string"}, "modules": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "<PERSON><PERSON><PERSON>"}, "dry_run": {"default": true, "title": "Dry Run", "type": "boolean"}}, "required": ["period"], "type": "object"}, "server_name": "sage-intacct", "last_updated": "2025-06-10 13:07:39.065430+00:00"}, {"name": "generate_consolidated_report", "description": "\nGenerate a consolidated report across modules.\n\nArgs:\n    report_type: Type of report (financial_summary, cash_flow, etc.)\n    start_date: Start date (YYYY-MM-DD)\n    end_date: End date (YYYY-MM-DD)\n    include_modules: Modules to include\n    format: Output format (json, csv, pdf)\n    \nReturns:\n    Consolidated report data\n", "parameters": {"properties": {"report_type": {"title": "Report Type", "type": "string"}, "start_date": {"title": "Start Date", "type": "string"}, "end_date": {"title": "End Date", "type": "string"}, "include_modules": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Include Mo<PERSON>les"}, "format": {"default": "json", "title": "Format", "type": "string"}}, "required": ["report_type", "start_date", "end_date"], "type": "object"}, "server_name": "sage-intacct", "last_updated": "2025-06-10 13:07:39.065435+00:00"}, {"name": "list_enabled_modules", "description": "\nList all enabled modules and their status.\n\nReturns:\n    Module status information\n", "parameters": {"properties": {}, "type": "object"}, "server_name": "sage-intacct", "last_updated": "2025-06-10 13:07:39.065439+00:00"}, {"name": "health_check", "description": "Check server health status.", "parameters": {"properties": {}, "type": "object"}, "server_name": "sage-intacct", "last_updated": "2025-06-10 13:07:39.065442+00:00"}, {"name": "start_authentication", "description": "Start the authentication flow with Sage Business Cloud Accounting.\n        \n        Args:\n            country: Optional 2-letter country code (e.g. gb, us, ca, fr, es, ie)\n                    to skip country selection screen\n            \n        Returns:\n            Authentication instructions or session information\n        ", "parameters": {"properties": {"country": {"default": null, "title": "Country", "type": "string"}}, "title": "start_authenticationArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863332+00:00"}, {"name": "complete_authentication", "description": "Complete the authentication flow by exchanging the code for tokens.\n        \n        Args:\n            authorization_code: The authorization code obtained from the redirect URL\n            code_verifier: Optional PKCE code verifier if session was lost. \n                           Strongly recommended to always provide this value.\n            \n        Returns:\n            Authentication result\n        ", "parameters": {"properties": {"authorization_code": {"title": "Authorization Code", "type": "string"}, "code_verifier": {"default": null, "title": "Code Verifier", "type": "string"}}, "required": ["authorization_code"], "title": "complete_authenticationArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863369+00:00"}, {"name": "capture_auth_token", "description": "Manually capture authentication code or tokens from browser cookies.\n        \n        Args:\n            callback_url: The full URL from your browser after authentication,\n                          including any hash fragments, cookies, or local storage data\n                          \n        Returns:\n            Authentication analysis and extraction results\n        ", "parameters": {"properties": {"callback_url": {"title": "Callback Url", "type": "string"}}, "required": ["callback_url"], "title": "capture_auth_tokenArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863380+00:00"}, {"name": "check_authentication_status", "description": "Check the current authentication status.\n        \n        Returns:\n            Authentication status information\n        ", "parameters": {"properties": {}, "title": "check_authentication_statusArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863388+00:00"}, {"name": "log_out", "description": "Log out and revoke the current authentication token.\n        \n        Returns:\n            Logout result\n        ", "parameters": {"properties": {}, "title": "log_outArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863395+00:00"}, {"name": "list_businesses", "description": "List all businesses the authenticated user has access to.\n        \n        Returns:\n            List of available businesses\n        ", "parameters": {"properties": {}, "title": "list_businessesArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863404+00:00"}, {"name": "select_business", "description": "Select a business for subsequent API operations.\n        \n        Args:\n            business_id: The business ID to select or \"lead\" for the lead business\n            \n        Returns:\n            Selection result\n        ", "parameters": {"properties": {"business_id": {"title": "Business Id", "type": "string"}}, "required": ["business_id"], "title": "select_businessArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863412+00:00"}, {"name": "easy_authenticate", "description": "Start a simplified authentication flow.\n        \n        This tool starts the authentication process with SBCA and provides\n        clear instructions on how to complete it with a single callback URL.\n        PKCE security is handled automatically in the background.\n        \n        Args:\n            country: Optional 2-letter country code (e.g. gb, us, ca, fr, es, ie)\n                    to skip country selection screen\n            \n        Returns:\n            Authentication instructions\n        ", "parameters": {"properties": {"country": {"default": null, "title": "Country", "type": "string"}}, "title": "easy_authenticateArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863419+00:00"}, {"name": "easy_authenticate_callback", "description": "Complete the simplified authentication flow.\n        \n        This tool handles extracting the authorization code and completing\n        the authentication process with automatic PKCE verification.\n        \n        Args:\n            callback_url: The full callback URL from your browser after authorization\n            \n        Returns:\n            Authentication result\n        ", "parameters": {"properties": {"callback_url": {"title": "Callback Url", "type": "string"}}, "required": ["callback_url"], "title": "easy_authenticate_callbackArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863426+00:00"}, {"name": "get_sales_invoices", "description": "\n        Returns all Sales Invoices with optional filtering and pagination.\n\n        Args:\n            search: Filter by invoice reference or contact name\n            contact_id: Filter by contact\n            status_id: Filter by status\n            from_date: Filter by sales invoice dates (ISO 8601 format)\n            to_date: Filter by sales invoice dates (ISO 8601 format)\n            updated_or_created_since: Limit to invoices changed since timestamp\n            deleted_since: Limit to invoices deleted since timestamp\n            has_attachments: Filter by attachment presence\n            items_per_page: Number of items per page (default 20, max 200)\n            page: Page number for pagination (default 1)\n            attributes: Specify attributes to expose\n            sort: Order by attributes (e.g., 'created_at', 'updated_at', 'date', 'due_date')\n            show_payments_allocations: Show artifact's payments and allocations\n        ", "parameters": {"properties": {"search": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Search"}, "contact_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Contact Id"}, "status_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Status Id"}, "from_date": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "From Date"}, "to_date": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "To Date"}, "updated_or_created_since": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Updated Or Created Since"}, "deleted_since": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Deleted Since"}, "has_attachments": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Has Attachments"}, "items_per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 20, "title": "Items Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}, "sort": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Sort"}, "show_payments_allocations": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Show Payments Allocations"}}, "title": "get_sales_invoicesArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863433+00:00"}, {"name": "get_sales_invoice_by_id", "description": "\n        Returns a specific Sales Invoice by its key.\n\n        Args:\n            invoice_id: The unique identifier for the sales invoice\n            show_payments_allocations: Show the invoice's payments and allocations\n            show_corrections: Show associated corrections\n            nested_attributes: Specify attributes for nested entities\n            mark_as_sent: Mark/not mark the artifact as sent (default: True)\n            show_recurring_invoice: Show associated recurring invoice template\n            show_analysis_types: Show line item analysis types\n            attributes: Specify attributes to expose\n        ", "parameters": {"properties": {"invoice_id": {"title": "Invoice Id", "type": "string"}, "show_payments_allocations": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Show Payments Allocations"}, "show_corrections": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Show Corrections"}, "nested_attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Nested Attributes"}, "mark_as_sent": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": true, "title": "<PERSON>"}, "show_recurring_invoice": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Show Recurring Invoice"}, "show_analysis_types": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Show Analysis Types"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "required": ["invoice_id"], "title": "get_sales_invoice_by_idArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863440+00:00"}, {"name": "get_sales_credit_notes", "description": "\n        Returns all Sales Credit Notes with optional filtering and pagination.\n\n        Args:\n            search: Filter by credit note reference or contact name\n            contact_id: Filter by contact\n            status_id: Filter by status\n            from_date: Filter by sales credit note dates (ISO 8601 format)\n            to_date: Filter by sales credit note dates (ISO 8601 format)\n            updated_or_created_since: Limit to credit notes changed since timestamp\n            deleted_since: Limit to credit notes deleted since timestamp\n            has_attachments: Filter by attachment presence\n            items_per_page: Number of items per page (default 20, max 200)\n            page: Page number for pagination (default 1)\n            attributes: Specify attributes to expose\n            sort: Order by attributes (e.g., 'created_at', 'updated_at', 'date')\n            show_payments_allocations: Show credit note's payments and allocations\n        ", "parameters": {"properties": {"search": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Search"}, "contact_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Contact Id"}, "status_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Status Id"}, "from_date": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "From Date"}, "to_date": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "To Date"}, "updated_or_created_since": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Updated Or Created Since"}, "deleted_since": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Deleted Since"}, "has_attachments": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Has Attachments"}, "items_per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 20, "title": "Items Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}, "sort": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Sort"}, "show_payments_allocations": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Show Payments Allocations"}}, "title": "get_sales_credit_notesArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863447+00:00"}, {"name": "get_sales_credit_note_by_id", "description": "\n        Returns a specific Sales Credit Note by its key.\n\n        Args:\n            credit_note_id: The unique identifier for the sales credit note\n            show_payments_allocations: Show the credit note's payments and allocations\n            nested_attributes: Specify attributes for nested entities\n            mark_as_sent: Mark/not mark the artifact as sent (default: True)\n            show_analysis_types: Show line item analysis types\n            attributes: Specify attributes to expose\n        ", "parameters": {"properties": {"credit_note_id": {"title": "Credit Note Id", "type": "string"}, "show_payments_allocations": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Show Payments Allocations"}, "nested_attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Nested Attributes"}, "mark_as_sent": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": true, "title": "<PERSON>"}, "show_analysis_types": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Show Analysis Types"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "required": ["credit_note_id"], "title": "get_sales_credit_note_by_idArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863454+00:00"}, {"name": "get_sales_quick_entries", "description": "\n        Returns all Sales Quick Entries with optional filtering and pagination.\n\n        Args:\n            contact_id: Filter by contact\n            search: Filter by quick entry reference or contact name\n            status_id: Filter by status\n            from_date: Filter by date (ISO 8601 format)\n            to_date: Filter by date (ISO 8601 format)\n            updated_or_created_since: Limit to entries changed since timestamp\n            deleted_since: Limit to entries deleted since timestamp\n            show_payments_allocations: Show payment and allocation information\n            has_attachments: Filter by attachment presence\n            items_per_page: Number of items per page (default 20, max 200)\n            page: Page number for pagination (default 1)\n            attributes: Specify attributes to expose\n            sort: Order by attributes (e.g., 'created_at', 'updated_at', 'date')\n        ", "parameters": {"properties": {"contact_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Contact Id"}, "search": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Search"}, "status_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Status Id"}, "from_date": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "From Date"}, "to_date": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "To Date"}, "updated_or_created_since": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Updated Or Created Since"}, "deleted_since": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Deleted Since"}, "show_payments_allocations": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Show Payments Allocations"}, "has_attachments": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Has Attachments"}, "items_per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 20, "title": "Items Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}, "sort": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Sort"}}, "title": "get_sales_quick_entriesArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863460+00:00"}, {"name": "get_sales_quick_entry_by_id", "description": "\n        Returns a specific Sales Quick Entry by its key.\n\n        Args:\n            quick_entry_id: The unique identifier for the sales quick entry\n            show_payments_allocations: Show the quick entry's payments and allocations\n            nested_attributes: Specify attributes for nested entities\n            show_analysis_types: Show line item analysis types\n            attributes: Specify attributes to expose\n        ", "parameters": {"properties": {"quick_entry_id": {"title": "Quick Entry Id", "type": "string"}, "show_payments_allocations": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Show Payments Allocations"}, "nested_attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Nested Attributes"}, "show_analysis_types": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Show Analysis Types"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "required": ["quick_entry_id"], "title": "get_sales_quick_entry_by_idArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863467+00:00"}, {"name": "get_contacts", "description": "Get a list of contacts with optional filtering capabilities.\n        \n        This tool retrieves contact data from Sage Business Cloud Accounting.\n        \n        Use this tool when the user asks about:\n        - List of all contacts\n        - Finding contacts by name or other attributes\n        - Contacts of a specific type (e.g., customer, supplier)\n        - Overview of the contact base\n        \n        Do NOT use this tool when the user is asking about a specific contact by ID,\n        or when they're asking about contact types rather than the contacts themselves.\n        \n        Args:\n            search_term: Optional text to search within contact names, references, or details\n            contact_type_id: Optional ID to filter contacts by their type\n            updated_or_created_since: Optional limit to contacts changed since a given date (format: YYYY-MM-DDT(+|-)hh:mm) or date-time (format: YYYY-MM-DDThh:mm:ss(+|-)hh:mm)\n            deleted_since: Optional limit to contacts deleted since a given date (format: YYYY-MM-DDT(+|-)hh:mm) or date-time (format: YYYY-MM-DDThh:mm:ss(+|-)hh:mm)\n            exclude_system: Optional filter to exclude system entities, not applicable for transaction creation\n            show_balance: Optional flag to display the balance for a contact\n            context_date: Optional date to determine the correct tax treatment for a contact (format: YYYY-MM-DD)\n            email: Optional filter to find contacts by their email address\n            show_unfinished_recurring_invoices_status: Optional flag to show unfinished recurring invoices status\n            filter_inactive_contacts: Optional filter to exclude inactive contacts\n            items_per_page: Number of items to return per page (default: 20, max: 200)\n            page: Page number to retrieve (default: 1)\n            attributes: Optional attributes to expose for the contacts (comma-separated or 'all')\n            \n        Returns:\n            Dictionary containing the list of contacts and metadata\n        ", "parameters": {"properties": {"search_term": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Search Term"}, "contact_type_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Contact Type Id"}, "updated_or_created_since": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Updated Or Created Since"}, "deleted_since": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Deleted Since"}, "exclude_system": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Exclude System"}, "show_balance": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Show Balance"}, "context_date": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Context Date"}, "email": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Email"}, "show_unfinished_recurring_invoices_status": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Show Unfinished Recurring Invoices Status"}, "filter_inactive_contacts": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Filter Inactive Contacts"}, "items_per_page": {"default": 20, "title": "Items Per Page", "type": "integer"}, "page": {"default": 1, "title": "Page", "type": "integer"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "title": "get_contactsArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863474+00:00"}, {"name": "get_contact_by_id", "description": "Get detailed information about a specific contact.\n        \n        This tool retrieves detailed information for a specific contact\n        from Sage Business Cloud Accounting.\n        \n        Use this tool when the user asks about:\n        - Details of a specific contact\n        - Information about a contact by ID or reference\n        - A contact's information, balance, or credit terms\n        \n        Do NOT use this tool for listing multiple contacts or when contact ID is unknown.\n        \n        Args:\n            contact_id: The ID of the contact to retrieve\n            nested_attributes: Optional attributes to expose for nested entities of the contact (comma-separated or 'all')\n            show_balance: Optional flag to display the balance for a contact\n            context_date: Optional date to determine the correct tax treatment for a contact (format: YYYY-MM-DD)\n            show_unfinished_recurring_invoices_status: Flag to show unfinished recurring invoices status (default: True)\n            attributes: Optional attributes to expose for the contact (comma-separated or 'all')\n            \n        Returns:\n            Dictionary containing detailed contact information\n        ", "parameters": {"properties": {"contact_id": {"title": "Contact Id", "type": "string"}, "nested_attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Nested Attributes"}, "show_balance": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Show Balance"}, "context_date": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Context Date"}, "show_unfinished_recurring_invoices_status": {"default": true, "title": "Show Unfinished Recurring Invoices Status", "type": "boolean"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "required": ["contact_id"], "title": "get_contact_by_idArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863480+00:00"}, {"name": "get_contact_types", "description": "Get a list of available contact types.\n        \n        This tool retrieves the list of contact types defined in\n        Sage Business Cloud Accounting.\n        \n        Use this tool when the user asks about:\n        - Types of contacts in the system\n        - Contact classifications or categories\n        - How contacts are segmented in the system\n        \n        Do NOT use this tool when the user is asking about specific contacts\n        or contact details, only use for type/category information.\n        \n        Args:\n            items_per_page: Number of items per page (default: 20, max: 200)\n            page: Page number (default: 1)\n            attributes: Optional attributes to expose (comma-separated or 'all')\n            \n        Returns:\n            Dictionary containing the list of contact types\n        ", "parameters": {"properties": {"items_per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 20, "title": "Items Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "title": "get_contact_typesArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863488+00:00"}, {"name": "get_purchase_invoices", "description": "Get a list of purchase invoices with optional filtering.\n        \n        This tool retrieves purchase invoices from Sage Business Cloud Accounting.\n        \n        Use this tool when the user asks about:\n        - List of purchase invoices\n        - Supplier expenses \n        - Purchase history\n        - Accounts payable\n        - Money owed to vendors\n        \n        Args:\n            contact_id: Optional supplier/contact ID to filter invoices by\n            search: Optional text to search within invoice reference or contact name\n            status_id: Optional filter by status (DRAFT, SUBMITTED, APPROVED, PAID, VOID)\n            from_date: Optional start date for invoices (ISO format: YYYY-MM-DD)\n            to_date: Optional end date for invoices (ISO format: YYYY-MM-DD)\n            updated_or_created_since: Optional limit to invoices changed since given date (ISO format)\n            deleted_since: Optional limit to invoices deleted since given date (ISO format)\n            has_attachments: Optional filter for invoices with attachments\n            items_per_page: Number of items to return per page (default: 20, max: 200)\n            page: Page number to retrieve (default: 1)\n            attributes: Optional attributes to expose for invoices (comma-separated or 'all')\n            sort: Optional sort parameter (e.g., \"created_at\", \"created_at:desc\")\n            show_payments_allocations: Optional flag to show the invoice's payments and allocations\n            \n        Returns:\n            Dictionary containing the list of purchase invoices and pagination metadata\n        ", "parameters": {"properties": {"contact_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Contact Id"}, "search": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Search"}, "status_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Status Id"}, "from_date": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "From Date"}, "to_date": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "To Date"}, "updated_or_created_since": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Updated Or Created Since"}, "deleted_since": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Deleted Since"}, "has_attachments": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Has Attachments"}, "items_per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 20, "title": "Items Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}, "sort": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Sort"}, "show_payments_allocations": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Show Payments Allocations"}}, "title": "get_purchase_invoicesArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863494+00:00"}, {"name": "get_purchase_invoice_by_id", "description": "Get detailed information about a specific purchase invoice.\n        \n        This tool retrieves detailed information for a specific purchase invoice \n        from Sage Business Cloud Accounting.\n        \n        Use this tool when the user asks about:\n        - Details of a specific purchase invoice\n        - Information about a purchase invoice by ID or number\n        - Line items on a particular purchase invoice\n        \n        Args:\n            invoice_id: The ID of the purchase invoice to retrieve\n            show_payments_allocations: Show the invoice's payments and allocations\n            show_corrections: Show associated corrections\n            nested_attributes: Specify attributes for nested entities\n            show_analysis_types: Show line item analysis types\n            attributes: Specify attributes to expose\n            \n        Returns:\n            Dictionary containing detailed purchase invoice information\n        ", "parameters": {"properties": {"invoice_id": {"title": "Invoice Id", "type": "string"}, "show_payments_allocations": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Show Payments Allocations"}, "show_corrections": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Show Corrections"}, "nested_attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Nested Attributes"}, "show_analysis_types": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Show Analysis Types"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "required": ["invoice_id"], "title": "get_purchase_invoice_by_idArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863501+00:00"}, {"name": "get_purchase_credit_notes", "description": "Get a list of purchase credit notes with optional filtering.\n        \n        This tool retrieves purchase credit notes from Sage Business Cloud Accounting.\n        \n        Use this tool when the user asks about:\n        - List of purchase credit notes\n        - Supplier refunds or credits\n        - Purchase returns\n        - Credit memos from vendors\n        \n        Args:\n            contact_id: Optional supplier/contact ID to filter credit notes by\n            search: Optional text to search within credit note reference or contact name\n            status_id: Optional filter by status (DRAFT, S<PERSON><PERSON>ITTED, APPROVED, PAID, VOID)\n            from_date: Optional start date for credit notes (ISO format: YYYY-MM-DD)\n            to_date: Optional end date for credit notes (ISO format: YYYY-MM-DD)\n            updated_or_created_since: Optional limit to credit notes changed since given date (ISO format)\n            deleted_since: Optional limit to credit notes deleted since given date (ISO format)\n            has_attachments: Optional filter for credit notes with attachments\n            items_per_page: Number of items to return per page (default: 20, max: 200)\n            page: Page number to retrieve (default: 1)\n            attributes: Optional attributes to expose for credit notes (comma-separated or 'all')\n            sort: Optional sort parameter (e.g., \"created_at\", \"created_at:desc\")\n            show_payments_allocations: Optional flag to show the credit note's payments and allocations\n            \n        Returns:\n            Dictionary containing the list of purchase credit notes and pagination metadata\n        ", "parameters": {"properties": {"contact_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Contact Id"}, "search": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Search"}, "status_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Status Id"}, "from_date": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "From Date"}, "to_date": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "To Date"}, "updated_or_created_since": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Updated Or Created Since"}, "deleted_since": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Deleted Since"}, "has_attachments": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Has Attachments"}, "items_per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 20, "title": "Items Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}, "sort": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Sort"}, "show_payments_allocations": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Show Payments Allocations"}}, "title": "get_purchase_credit_notesArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863507+00:00"}, {"name": "get_purchase_credit_note_by_id", "description": "Get detailed information about a specific purchase credit note.\n        \n        This tool retrieves detailed information for a specific purchase credit note \n        from Sage Business Cloud Accounting.\n        \n        Use this tool when the user asks about:\n        - Details of a specific purchase credit note\n        - Information about a purchase credit note by ID or number\n        - Line items on a particular purchase credit note\n        \n        Args:\n            credit_note_id: The ID of the purchase credit note to retrieve\n            show_payments_allocations: Show the credit note's payments and allocations\n            nested_attributes: Specify attributes for nested entities\n            show_analysis_types: Show line item analysis types\n            attributes: Specify attributes to expose\n            \n        Returns:\n            Dictionary containing detailed purchase credit note information\n        ", "parameters": {"properties": {"credit_note_id": {"title": "Credit Note Id", "type": "string"}, "show_payments_allocations": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Show Payments Allocations"}, "nested_attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Nested Attributes"}, "show_analysis_types": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Show Analysis Types"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "required": ["credit_note_id"], "title": "get_purchase_credit_note_by_idArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863513+00:00"}, {"name": "get_purchase_quick_entries", "description": "Get a list of purchase quick entries for a specified period.\n        \n        This tool retrieves purchase quick entries from Sage Business Cloud Accounting.\n        Quick entries are simplified purchase transactions that don't require full invoices.\n        \n        Use this tool when the user asks about:\n        - Quick purchase entries\n        - Simple purchase transactions\n        - Cash purchases\n        - Purchases without formal invoices\n        \n        Args:\n            contact_id: Optional supplier/contact ID to filter entries by\n            search: Optional text to search within entry reference or contact name\n            status_id: Optional filter by status (DRAFT, PAID, VOID)\n            from_date: Optional start date for entries (ISO format: YYYY-MM-DD)\n            to_date: Optional end date for entries (ISO format: YYYY-MM-DD)\n            updated_or_created_since: Optional limit to entries changed since given date (ISO format)\n            deleted_since: Optional limit to entries deleted since given date (ISO format)\n            show_payments_allocations: Optional flag to show the entry's payments and allocations\n            has_attachments: Optional filter for entries with attachments\n            items_per_page: Number of items to return per page (default: 20, max: 200)\n            page: Page number to retrieve (default: 1)\n            attributes: Optional attributes to expose for entries (comma-separated or 'all')\n            sort: Optional sort parameter (e.g., \"created_at\", \"created_at:desc\")\n            \n        Returns:\n            Dictionary containing the list of purchase quick entries and metadata\n        ", "parameters": {"properties": {"contact_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Contact Id"}, "search": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Search"}, "status_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Status Id"}, "from_date": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "From Date"}, "to_date": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "To Date"}, "updated_or_created_since": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Updated Or Created Since"}, "deleted_since": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Deleted Since"}, "show_payments_allocations": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Show Payments Allocations"}, "has_attachments": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Has Attachments"}, "items_per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 20, "title": "Items Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}, "sort": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Sort"}}, "title": "get_purchase_quick_entriesArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863520+00:00"}, {"name": "get_purchase_quick_entry_by_id", "description": "Get detailed information about a specific purchase quick entry.\n        \n        This tool retrieves detailed information for a specific purchase quick entry\n        from Sage Business Cloud Accounting.\n        \n        Use this tool when the user asks about:\n        - Details of a specific purchase quick entry\n        - Information about a purchase quick entry by ID or number\n        \n        Args:\n            quick_entry_id: The ID of the purchase quick entry to retrieve\n            show_payments_allocations: Show the quick entry's payments and allocations\n            nested_attributes: Specify attributes for nested entities\n            show_analysis_types: Show line item analysis types\n            attributes: Specify attributes to expose\n            \n        Returns:\n            Dictionary containing detailed purchase quick entry information\n        ", "parameters": {"properties": {"quick_entry_id": {"title": "Quick Entry Id", "type": "string"}, "show_payments_allocations": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Show Payments Allocations"}, "nested_attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Nested Attributes"}, "show_analysis_types": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Show Analysis Types"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "required": ["quick_entry_id"], "title": "get_purchase_quick_entry_by_idArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863527+00:00"}, {"name": "get_products", "description": "Get a list of products with optional filtering capabilities.\n        \n        This tool retrieves product data from Sage Business Cloud Accounting.\n        \n        Use this tool to:\n        - List all products\n        - Search for products by name, code, or description\n        - Filter products by type (stock item, service, non-stock)\n        - Get active or inactive products\n        \n        Args:\n            search: Optional text to search within product names, codes, or descriptions\n            catalog_item_type: Optional filter for product type (PRODUCT, STOCK_ITEM, SERVICE)\n            active: Whether to return only active products (default: True)\n            updated_or_created_since: Filter by items changed since a given date (ISO format)\n            deleted_since: Filter by items deleted since a given date (ISO format)\n            items_per_page: Number of items per page (default: 20, max: 200)\n            page: Page number to retrieve (default: 1)\n            \n        Returns:\n            Dictionary containing list of products and metadata\n        ", "parameters": {"properties": {"search": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Search"}, "catalog_item_type": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Catalog Item Type"}, "active": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": true, "title": "Active"}, "updated_or_created_since": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Updated Or Created Since"}, "deleted_since": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Deleted Since"}, "items_per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 20, "title": "Items Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}}, "title": "get_productsArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863533+00:00"}, {"name": "get_product_by_id", "description": "Get detailed information about a specific product.\n        \n        This tool retrieves detailed information for a specific product\n        from Sage Business Cloud Accounting.\n        \n        Use this tool when you need:\n        - Detailed product information\n        - Pricing details\n        - Product specifications\n        \n        Args:\n            product_id: Unique identifier for the product\n            \n        Returns:\n            Dictionary with comprehensive product details\n        ", "parameters": {"properties": {"product_id": {"title": "Product Id", "type": "string"}}, "required": ["product_id"], "title": "get_product_by_idArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863541+00:00"}, {"name": "get_stock_items", "description": "Get information about stock items with optional filtering.\n        \n        This tool retrieves stock item data from Sage Business Cloud Accounting.\n        \n        Use this tool to:\n        - List available stock items and current quantities\n        - Search for specific stock items by name or code\n        - Filter items that are out of stock or below reorder level\n        - Find items based on their active status\n        \n        Args:\n            search: Optional text to search within stock item names, codes, or descriptions\n            updated_or_created_since: Filter by items changed since a given date (ISO format)\n            deleted_since: Filter by items deleted since a given date (ISO format)\n            active: Whether to return only active items (default: True)\n            out_of_stock: Whether to return only items with zero quantity (default: False)\n            below_reorder_level: Whether to return only items below reorder level (default: False)\n            items_per_page: Number of items per page (default: 20, max: 200)\n            page: Page number to retrieve (default: 1)\n            attributes: Optional attributes to expose for the stock items\n            \n        Returns:\n            Dictionary containing list of stock items and metadata\n        ", "parameters": {"properties": {"search": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Search"}, "updated_or_created_since": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Updated Or Created Since"}, "deleted_since": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Deleted Since"}, "active": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": true, "title": "Active"}, "out_of_stock": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": false, "title": "Out Of Stock"}, "below_reorder_level": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": false, "title": "Below Reorder Level"}, "items_per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 20, "title": "Items Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "title": "get_stock_itemsArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863548+00:00"}, {"name": "get_stock_movements", "description": "Retrieve stock movement history with filtering options.\n        \n        This tool retrieves the history of stock movements from Sage Business Cloud Accounting,\n        such as purchases, sales, adjustments, and transfers.\n        \n        Use this tool to:\n        - Track inventory changes over time\n        - View stock receipts and dispatches\n        - Monitor adjustments and transfers\n        - Analyze stock movement patterns by date or location\n        \n        Args:\n            search: Optional text to search within details or references\n            stock_item_id: Optional filter for a specific stock item\n            from_date: Optional start date for movements (ISO format: YYYY-MM-DD)\n            to_date: Optional end date for movements (ISO format: YYYY-MM-DD)\n            items_per_page: Number of items per page (default: 20, max: 200)\n            page: Page number to retrieve (default: 1)\n            attributes: Optional attributes to expose for the stock movements\n            \n        Returns:\n            Dictionary containing list of stock movements and metadata\n        ", "parameters": {"properties": {"search": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Search"}, "stock_item_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Stock Item Id"}, "from_date": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "From Date"}, "to_date": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "To Date"}, "items_per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 20, "title": "Items Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "title": "get_stock_movementsArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863554+00:00"}, {"name": "get_services", "description": "Get a list of services with optional filtering capabilities.\n        \n        This tool retrieves service data from Sage Business Cloud Accounting.\n        \n        Use this tool to:\n        - List all services\n        - Search for services by name or description\n        - Filter services by type (professional, installation, support, etc.)\n        - Get active or inactive services\n        \n        Args:\n            search: Optional text to search within service names, codes, or descriptions\n            service_type_id: Optional filter for service type (PROFESSIONAL, INSTALLATION, etc.)\n            active: Whether to return only active services (default: True)\n            updated_or_created_since: Filter by items changed since a given date (ISO format)\n            deleted_since: Filter by items deleted since a given date (ISO format)\n            items_per_page: Number of items per page (default: 20, max: 200)\n            page: Page number to retrieve (default: 1)\n            \n        Returns:\n            Dictionary containing list of services and metadata\n        ", "parameters": {"properties": {"search": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Search"}, "service_type_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Service Type Id"}, "active": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": true, "title": "Active"}, "updated_or_created_since": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Updated Or Created Since"}, "deleted_since": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Deleted Since"}, "items_per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 20, "title": "Items Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}}, "title": "get_servicesArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863560+00:00"}, {"name": "get_service_by_id", "description": "Get detailed information about a specific service.\n        \n        This tool retrieves detailed information for a specific service\n        from Sage Business Cloud Accounting.\n        \n        Use this tool when you need:\n        - Detailed service information\n        - Pricing details\n        - Service specifications\n        \n        Args:\n            service_id: Unique identifier for the service\n            \n        Returns:\n            Dictionary with comprehensive service details\n        ", "parameters": {"properties": {"service_id": {"title": "Service Id", "type": "string"}}, "required": ["service_id"], "title": "get_service_by_idArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863567+00:00"}, {"name": "get_service_rate_types", "description": "Returns all Service Rate Types.\n        \n        This tool retrieves service rate types data from Sage Business Cloud Accounting.\n        \n        A service can have several different types of rates. You can define your own rate types \n        and use them in the rates listed at the attribute 'sales_rates' when creating a service, \n        or just use one of the preconfigured service rate types.\n        \n        Use this tool when you need:\n        - A list of available service rate types\n        - Information about billing structures for services\n        - Reference data for creating or updating services\n        \n        Args:\n            updated_or_created_since: Filter by items changed since a given date (ISO format)\n            active: Whether to return only active or inactive items\n            items_per_page: Number of items per page (default: 20, max: 200)\n            page: Page number to retrieve (default: 1)\n            attributes: Attributes to include in response\n            \n        Returns:\n            Dictionary containing list of service rate types and metadata\n        ", "parameters": {"properties": {"updated_or_created_since": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Updated Or Created Since"}, "active": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Active"}, "items_per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 20, "title": "Items Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "title": "get_service_rate_typesArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863573+00:00"}, {"name": "get_service_rate_type_by_id", "description": "Returns a Service Rate Type.\n        \n        This tool retrieves detailed information for a specific service rate type\n        from Sage Business Cloud Accounting.\n        \n        Use this tool when you need:\n        - Details about a specific service rate type\n        - Properties of a particular service billing structure\n        \n        Args:\n            service_rate_type_id: Unique identifier for the service rate type\n            \n        Returns:\n            Dictionary with service rate type details\n        ", "parameters": {"properties": {"service_rate_type_id": {"title": "Service Rate Type Id", "type": "string"}}, "required": ["service_rate_type_id"], "title": "get_service_rate_type_by_idArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863579+00:00"}, {"name": "get_trial_balance", "description": "Returns a trial balance for a date range.\n        \n        This tool retrieves trial balance data from Sage Business Cloud Accounting,\n        showing all the general ledger accounts with their debit and credit balances.\n        \n        A trial balance is a list of all the general ledger accounts (both revenue and capital).\n        Each nominal ledger account will hold either a debit balance or a credit balance.\n        Additionally to the balances at the start and end dates you will get the sums of all\n        debit and credit transactions during the specified period.\n        \n        Args:\n            from_date: The first day in the date range to query the balance from (ISO format: YYYY-MM-DD)\n            to_date: The last day in the date range to query the balance from (ISO format: YYYY-MM-DD)\n            \n        Returns:\n            Dictionary containing trial balance data including ledger accounts with their balances\n        ", "parameters": {"properties": {"from_date": {"title": "From Date", "type": "string"}, "to_date": {"title": "To Date", "type": "string"}}, "required": ["from_date", "to_date"], "title": "get_trial_balanceArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863586+00:00"}, {"name": "get_contact_allocations", "description": "Returns all Contact Allocations.\n        \n        This endpoint allows you to allocate e.g. a sales credit note to a sales invoice, \n        thus paying the invoice with the credit note. A contact allocation is therefore a \n        transaction from one such artefact to another, given they have the same contact. \n        The actual amount recorded needs to be specified for each artefact allocated, and \n        all amounts need to sum up to zero.\n        \n        Args:\n            contact_id: Use this to filter by contact id\n            transaction_type_id: Use this to filter by transaction type id\n            updated_or_created_since: Use this to limit the response to Allocations changed since a given date (format: YYYY-MM-DDT(+|-)hh:mm) or date-time (format: YYYY-MM-DDThh:mm:ss(+|-)hh:mm). Inclusive of the passed timestamp.\n            deleted_since: Use this to limit the response to Allocations deleted since a given date (format: YYYY-MM-DDT(+|-)hh:mm) or date-time (format: YYYY-MM-DDThh:mm:ss(+|-)hh:mm). Not inclusive of the passed timestamp.\n            items_per_page: Returns the given number of Allocations per request.\n            page: Go to specific page of Allocations\n            attributes: Specify the attributes that you want to expose for the Allocations (expose all attributes with 'all'). These are in addition to the base attributes (name, path)\n            sort: Order by a given attribute (required) and direction (optional; `asc` or `desc`; defaults to `asc`). Available attributes are: created_at, updated_at, date\n            \n        Returns:\n            Dictionary containing list of contact allocations and metadata\n        ", "parameters": {"properties": {"contact_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Contact Id"}, "transaction_type_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Transaction Type Id"}, "updated_or_created_since": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Updated Or Created Since"}, "deleted_since": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Deleted Since"}, "items_per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 20, "title": "Items Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}, "sort": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Sort"}}, "title": "get_contact_allocationsArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863593+00:00"}, {"name": "get_contact_allocation_by_id", "description": "Returns a Contact Allocation.\n        \n        This tool retrieves detailed information for a specific contact allocation\n        from Sage Business Cloud Accounting.\n        \n        Args:\n            key: The Contact Allocation Key.\n            attributes: Specify the attributes that you want to expose for the Allocation (expose all attributes with 'all'). These are in addition to the base attributes (name, path)\n            \n        Returns:\n            Dictionary containing detailed contact allocation information\n        ", "parameters": {"properties": {"key": {"title": "Key", "type": "string"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "required": ["key"], "title": "get_contact_allocation_by_idArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863600+00:00"}, {"name": "get_payment_methods", "description": "Returns all Payment Methods.\n        \n        This is a static endpoint, providing you with consistent payment method IDs to be used at the key payment_method_id in /contact_payments, /other_payments, and as default_payment_method_id /bank_account upon creating of updating resources. Naturally, only reading access is possible.\n        \n        Args:\n            items_per_page: Returns the given number of Payment Types per request.\n            page: Go to specific page of Payment Types\n            attributes: Specify the attributes that you want to expose for the Payment Types (expose all attributes with 'all'). These are in addition to the base attributes (name, path)\n            \n        Returns:\n            Dictionary containing the list of payment methods\n        ", "parameters": {"properties": {"items_per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 20, "title": "Items Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "title": "get_payment_methodsArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863606+00:00"}, {"name": "get_payment_method_by_id", "description": "Returns a Payment Method.\n        \n        This tool retrieves a specific payment method by its key from Sage Business Cloud Accounting.\n        \n        Args:\n            key: The Payment Method Key.\n            attributes: Specify the attributes that you want to expose for the Payment Type (expose all attributes with 'all'). These are in addition to the base attributes (name, path)\n            \n        Returns:\n            Dictionary containing detailed payment method information\n        ", "parameters": {"properties": {"key": {"title": "Key", "type": "string"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "required": ["key"], "title": "get_payment_method_by_idArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863612+00:00"}, {"name": "get_contact_payments", "description": "Returns all Contact Payments.\n        \n        A contact payment is a payment that relates to a contact and an artefact that requires payment, i.e. usually an invoice. Incoming and outgoing payments are both handled in this endpoint; use the transaction_type_id to distinguish payments from receipts (from the perspective of the business).\n        \n        Args:\n            contact_id: Use this to filter by contact id\n            bank_account_id: Use this to filter by bank account id\n            transaction_type_id: Use this to filter by transaction type id\n            updated_or_created_since: Use this to limit the response to Artefact Payments changed since a given date (format: YYYY-MM-DDT(+|-)hh:mm) or date-time (format: YYYY-MM-DDThh:mm:ss(+|-)hh:mm). Inclusive of the passed timestamp.\n            deleted_since: Use this to limit the response to Artefact Payments deleted since a given date (format: YYYY-MM-DDT(+|-)hh:mm) or date-time (format: YYYY-MM-DDThh:mm:ss(+|-)hh:mm). Not inclusive of the passed timestamp.\n            from_date: Use this to filter by Artefact Payments dates\n            to_date: Use this to filter by Artefact Payments dates\n            items_per_page: Returns the given number of Artefact Payments per request.\n            page: Go to specific page of Artefact Payments\n            attributes: Specify the attributes that you want to expose for the Artefact Payments (expose all attributes with 'all'). These are in addition to the base attributes (name, path)\n            sort: Order by a given attribute (required) and direction (optional; `asc` or `desc`; defaults to `asc`).\n                  Available attributes are: created_at, updated_at, date\n                  Example: `sort=created_at` or `sort=created_at:desc`\n            \n        Returns:\n            Dictionary containing list of contact payments and metadata\n        ", "parameters": {"properties": {"contact_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Contact Id"}, "bank_account_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Bank Account Id"}, "transaction_type_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Transaction Type Id"}, "updated_or_created_since": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Updated Or Created Since"}, "deleted_since": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Deleted Since"}, "from_date": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "From Date"}, "to_date": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "To Date"}, "items_per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 20, "title": "Items Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}, "sort": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Sort"}}, "title": "get_contact_paymentsArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863618+00:00"}, {"name": "get_contact_payment_by_id", "description": "Returns a Contact Payment.\n        \n        This tool retrieves detailed information for a specific contact payment\n        from Sage Business Cloud Accounting.\n        \n        Args:\n            key: The Contact Payment Key.\n            attributes: Specify the attributes that you want to expose for the Artefact Payment (expose all attributes with 'all'). These are in addition to the base attributes (name, path)\n            \n        Returns:\n            Dictionary containing detailed contact payment information\n        ", "parameters": {"properties": {"key": {"title": "Key", "type": "string"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "required": ["key"], "title": "get_contact_payment_by_idArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863625+00:00"}, {"name": "get_other_payments", "description": "Returns all Other Payments.\n        \n        Just as a Contact Payment, an Other Payment relates to a contact, but instead of an \n        artefact that requires payment you need to specify a ledger account as well as the \n        amount that is recorded to it. Thus, you can use this endpoint when you need to book \n        a contact's payment that cannot be associated with a payment-requiring artefact like \n        an invoice. Incoming and outgoing payments are both handled in this endpoint; use the \n        transaction_type_id to distinguish payments from receipts (from the perspective of the business).\n        \n        Args:\n            bank_account_id: Use this to filter by bank account id\n            contact_id: Use this to filter by contact id\n            deleted_since: Use this to limit the response to Payments deleted since a given date (format: YYYY-MM-DDT(+|-)hh:mm) or date-time (format: YYYY-MM-DDThh:mm:ss(+|-)hh:mm). Not inclusive of the passed timestamp.\n            from_date: Use this to filter by Payments dates\n            has_attachments: Use this to filter Payments by whether they have attachments or not\n            to_date: Use this to filter by Payments dates\n            transaction_type_id: Use this to filter by transaction type id\n            updated_or_created_since: Use this to limit the response to Payments changed since a given date (format: YYYY-MM-DDT(+|-)hh:mm) or date-time (format: YYYY-MM-DDThh:mm:ss(+|-)hh:mm). Inclusive of the passed timestamp.\n            items_per_page: Returns the given number of Payments per request.\n            page: Go to specific page of Payments\n            attributes: Specify the attributes that you want to expose for the Payments (expose all attributes with 'all'). These are in addition to the base attributes (name, path)\n            sort: Order by a given attribute (required) and direction (optional; `asc` or `desc`; defaults to `asc`). Available attributes are: created_at, updated_at, date\n            \n        Returns:\n            Dictionary containing list of other payments and metadata\n        ", "parameters": {"properties": {"bank_account_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Bank Account Id"}, "contact_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Contact Id"}, "deleted_since": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Deleted Since"}, "from_date": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "From Date"}, "has_attachments": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Has Attachments"}, "to_date": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "To Date"}, "transaction_type_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Transaction Type Id"}, "updated_or_created_since": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Updated Or Created Since"}, "items_per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 20, "title": "Items Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}, "sort": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Sort"}}, "title": "get_other_paymentsArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863631+00:00"}, {"name": "get_other_payment_by_id", "description": "Returns a Other Payment.\n        \n        This tool retrieves detailed information for a specific other payment\n        from Sage Business Cloud Accounting.\n        \n        Args:\n            key: The Other Payment Key.\n            nested_attributes: Specify the attributes that you want to expose for nested entities of the Payment (expose all nested attributes with 'all'). These are in addition to the base attributes (name, path)\n            show_analysis_types: Use this to show the line item analysis types\n            attributes: Specify the attributes that you want to expose for the Payment (expose all attributes with 'all'). These are in addition to the base attributes (name, path)\n            \n        Returns:\n            Dictionary containing detailed other payment information\n        ", "parameters": {"properties": {"key": {"title": "Key", "type": "string"}, "nested_attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Nested Attributes"}, "show_analysis_types": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Show Analysis Types"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "required": ["key"], "title": "get_other_payment_by_idArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863638+00:00"}, {"name": "get_unallocated_artefacts", "description": "Returns all Unallocated Artefacts.\n        \n        This endpoint compiles a list of all transactions which have an outstanding amount. \n        These are specifically (purchase or sales) invoices, credit notes, quick entries, \n        corrective invoices, contact payments and opening balances.\n        \n        Args:\n            contact_id: Use this to filter by contact id\n            search: Use this to filter by the contact identifier\n            attributes: Specify the attributes that you want to expose for the Base Artefacts (expose all attributes with 'all'). These are in addition to the base attributes (name, path)\n            items_per_page: Returns the given number of Base Artefacts per request\n            page: Go to specific page of Base Artefacts\n            \n        Returns:\n            Dictionary containing list of unallocated artefacts and metadata\n        ", "parameters": {"properties": {"contact_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Contact Id"}, "search": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Search"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}, "items_per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 20, "title": "Items Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}}, "title": "get_unallocated_artefactsArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863645+00:00"}, {"name": "get_unallocated_artefact_by_id", "description": "Returns a Unallocated Artefact.\n        \n        This tool retrieves detailed information for a specific unallocated artefact\n        from Sage Business Cloud Accounting.\n        \n        Args:\n            key: The Unallocated Artefact Key.\n            attributes: Specify the attributes that you want to expose for the Base Artefact (expose all attributes with 'all'). These are in addition to the base attributes (name, path)\n            \n        Returns:\n            Dictionary containing detailed unallocated artefact information\n        ", "parameters": {"properties": {"key": {"title": "Key", "type": "string"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "required": ["key"], "title": "get_unallocated_artefact_by_idArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863652+00:00"}, {"name": "get_bank_accounts", "description": "Returns all Bank Accounts with optional filtering.\n        \n        This tool retrieves bank account data from Sage Business Cloud Accounting.\n        \n        Use this tool when the user asks about:\n        - List of all bank accounts\n        - Finding bank accounts by attributes\n        - Overview of the bank accounts\n        \n        Do NOT use this tool when the user is asking about a specific bank account by ID.\n        \n        Args:\n            updated_or_created_since: Optional limit to Bank Accounts changed since a given date\n                                    (format: YYYY-MM-DDT(+|-)hh:mm) or date-time\n                                    (format: YYYY-MM-DDThh:mm:ss(+|-)hh:mm)\n            deleted_since: Optional limit to Bank Accounts deleted since a given date\n                         (format: YYYY-MM-DDT(+|-)hh:mm) or date-time\n                         (format: YYYY-MM-DDThh:mm:ss(+|-)hh:mm)\n            nested_attributes: Optional attributes to expose for nested entities\n            exclude_stripe: Optional filter to exclude Stripe Bank Accounts\n            filter_inactive_bank_accounts: Optional filter to exclude inactive accounts\n            appName: Optional App Name for filtering\n            items_per_page: Number of items to return per page (default: 20, max: 200)\n            page: Page number to retrieve (default: 1)\n            attributes: Optional attributes to expose for bank accounts\n            \n        Returns:\n            Dictionary containing the list of bank accounts and metadata\n        ", "parameters": {"properties": {"updated_or_created_since": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Updated Or Created Since"}, "deleted_since": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Deleted Since"}, "nested_attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Nested Attributes"}, "exclude_stripe": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Exclude Stripe"}, "filter_inactive_bank_accounts": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Filter Inactive Bank Accounts"}, "appName": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Appname"}, "items_per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 20, "title": "Items Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "title": "get_bank_accountsArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863658+00:00"}, {"name": "get_bank_account_by_id", "description": "Returns details for a specific Bank Account.\n        \n        This tool retrieves detailed information for a specific bank account\n        from Sage Business Cloud Accounting.\n        \n        Use this tool when the user asks about:\n        - Details of a specific bank account\n        - Information about a bank account by ID\n        - Balance of a specific bank account\n        \n        Do NOT use this tool for listing multiple bank accounts.\n        \n        Args:\n            key: The Bank Account Key (ID)\n            nested_attributes: Optional attributes to expose for nested entities\n            attributes: Optional attributes to expose for the bank account\n            \n        Returns:\n            Dictionary containing detailed bank account information\n        ", "parameters": {"properties": {"key": {"title": "Key", "type": "string"}, "nested_attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Nested Attributes"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "required": ["key"], "title": "get_bank_account_by_idArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863665+00:00"}, {"name": "get_bank_account_bank_feed", "description": "Returns the bank feed information for a bank account.\n        \n        This tool retrieves bank feed information for Banking Cloud Bank accounts\n        from Sage Business Cloud Accounting.\n        \n        Use this tool when the user asks about:\n        - Bank feed information for a specific bank account\n        - Bank feed connection status\n        - Bank feed configuration\n        \n        Args:\n            key: The Bank Account GUID\n            \n        Returns:\n            Dictionary containing bank feed information\n        ", "parameters": {"properties": {"key": {"title": "Key", "type": "string"}}, "required": ["key"], "title": "get_bank_account_bank_feedArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863671+00:00"}, {"name": "get_bank_account_types", "description": "Returns all Bank Account Types.\n        \n        This tool retrieves bank account type data from Sage Business Cloud Accounting.\n        \n        Use this tool when the user asks about:\n        - Types of bank accounts in the system\n        - Bank account classifications or categories\n        - Available bank account types\n        \n        Do NOT use this tool when the user is asking about a specific bank account type by ID.\n        \n        Args:\n            items_per_page: Number of items to return per page (default: 20, max: 200)\n            page: Page number to retrieve (default: 1)\n            attributes: Optional attributes to expose for bank account types\n            \n        Returns:\n            Dictionary containing the list of bank account types and metadata\n        ", "parameters": {"properties": {"items_per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 20, "title": "Items Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "title": "get_bank_account_typesArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863678+00:00"}, {"name": "get_bank_account_type_by_id", "description": "Returns details for a specific Bank Account Type.\n        \n        This tool retrieves detailed information for a specific bank account type\n        from Sage Business Cloud Accounting.\n        \n        Use this tool when the user asks about:\n        - Details of a specific bank account type\n        - Information about a bank account type by ID or name\n        - Specific bank account type properties\n        \n        Do NOT use this tool for listing multiple bank account types.\n        \n        Args:\n            key: The Bank Account Type Key (ID)\n            attributes: Optional attributes to expose for the bank account type\n            \n        Returns:\n            Dictionary containing detailed bank account type information\n        ", "parameters": {"properties": {"key": {"title": "Key", "type": "string"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "required": ["key"], "title": "get_bank_account_type_by_idArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863685+00:00"}, {"name": "get_bank_deposits", "description": "Returns all Bank Deposits with optional filtering.\n        \n        This tool retrieves bank deposit data from Sage Business Cloud Accounting.\n        \n        Use this tool when the user asks about:\n        - List of bank deposits\n        - Cash deposits\n        - Deposits to bank accounts\n        - Transfer of funds from cash to bank accounts\n        \n        Do NOT use this tool when the user is asking about a specific bank deposit by ID.\n        \n        Args:\n            updated_or_created_since: Optional limit to Deposits changed since a given date\n                                    (format: YYYY-MM-DDT(+|-)hh:mm) or date-time\n                                    (format: YYYY-MM-DDThh:mm:ss(+|-)hh:mm)\n            deleted_since: Optional limit to Deposits deleted since a given date\n                         (format: YYYY-MM-DDT(+|-)hh:mm) or date-time\n                         (format: YYYY-MM-DDThh:mm:ss(+|-)hh:mm)\n            from_date: Optional filter by Deposits dates\n            to_date: Optional filter by Deposits dates\n            items_per_page: Number of items to return per page (default: 20, max: 200)\n            page: Page number to retrieve (default: 1)\n            attributes: Optional attributes to expose for bank deposits\n            sort: Optional sort parameter (e.g., \"created_at\", \"created_at:desc\")\n            \n        Returns:\n            Dictionary containing the list of bank deposits and metadata\n        ", "parameters": {"properties": {"updated_or_created_since": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Updated Or Created Since"}, "deleted_since": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Deleted Since"}, "from_date": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "From Date"}, "to_date": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "To Date"}, "items_per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 20, "title": "Items Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}, "sort": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Sort"}}, "title": "get_bank_depositsArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863691+00:00"}, {"name": "get_bank_deposit_by_id", "description": "Returns details for a specific Bank Deposit.\n        \n        This tool retrieves detailed information for a specific bank deposit\n        from Sage Business Cloud Accounting.\n        \n        Use this tool when the user asks about:\n        - Details of a specific bank deposit\n        - Information about a bank deposit by ID\n        - Specific bank deposit transaction\n        \n        Do NOT use this tool for listing multiple bank deposits.\n        \n        Args:\n            key: The Bank Deposit Key (ID)\n            nested_attributes: Optional attributes to expose for nested entities\n            attributes: Optional attributes to expose for the bank deposit\n            \n        Returns:\n            Dictionary containing detailed bank deposit information\n        ", "parameters": {"properties": {"key": {"title": "Key", "type": "string"}, "nested_attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Nested Attributes"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "required": ["key"], "title": "get_bank_deposit_by_idArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863697+00:00"}, {"name": "get_bank_transfers", "description": "Returns all Bank Transfers with optional filtering.\n        \n        This tool retrieves bank transfer data from Sage Business Cloud Accounting.\n        \n        Use this tool when the user asks about:\n        - List of bank transfers\n        - Money movements between bank accounts\n        - Transfers of funds between accounts\n        - History of account transfers\n        \n        Do NOT use this tool when the user is asking about a specific bank transfer by ID,\n        or when they're asking about cash deposits (use bank_deposits instead).\n        \n        Args:\n            updated_or_created_since: Optional limit to Bank Transfers changed since a given date\n                                    (format: YYYY-MM-DDT(+|-)hh:mm) or date-time\n                                    (format: YYYY-MM-DDThh:mm:ss(+|-)hh:mm)\n            deleted_since: Optional limit to Bank Transfers deleted since a given date\n                         (format: YYYY-MM-DDT(+|-)hh:mm) or date-time\n                         (format: YYYY-MM-DDThh:mm:ss(+|-)hh:mm)\n            from_date: Optional filter by Bank Transfers dates\n            to_date: Optional filter by Bank Transfers dates\n            items_per_page: Number of items to return per page (default: 20, max: 200)\n            page: Page number to retrieve (default: 1)\n            attributes: Optional attributes to expose for bank transfers\n            sort: Optional sort parameter (e.g., \"created_at\", \"created_at:desc\")\n            \n        Returns:\n            Dictionary containing the list of bank transfers and metadata\n        ", "parameters": {"properties": {"updated_or_created_since": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Updated Or Created Since"}, "deleted_since": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Deleted Since"}, "from_date": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "From Date"}, "to_date": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "To Date"}, "items_per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 20, "title": "Items Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}, "sort": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Sort"}}, "title": "get_bank_transfersArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863704+00:00"}, {"name": "get_bank_transfer_by_id", "description": "Returns details for a specific Bank Transfer.\n        \n        This tool retrieves detailed information for a specific bank transfer\n        from Sage Business Cloud Accounting.\n        \n        Use this tool when the user asks about:\n        - Details of a specific bank transfer\n        - Information about a bank transfer by ID\n        - Specific transfer transaction details\n        \n        Do NOT use this tool for listing multiple bank transfers.\n        \n        Args:\n            key: The Bank Transfer Key (ID)\n            nested_attributes: Optional attributes to expose for nested entities\n            attributes: Optional attributes to expose for the bank transfer\n            \n        Returns:\n            Dictionary containing detailed bank transfer information\n        ", "parameters": {"properties": {"key": {"title": "Key", "type": "string"}, "nested_attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Nested Attributes"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "required": ["key"], "title": "get_bank_transfer_by_idArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863711+00:00"}, {"name": "get_bank_reconciliations", "description": "Returns all Bank Reconciliations with optional filtering.\n        \n        This tool retrieves bank reconciliation data from Sage Business Cloud Accounting.\n        Bank reconciliations are used to match the business bank accounts to real-life\n        bank statements and transactions.\n        \n        Use this tool when the user asks about:\n        - List of bank reconciliations\n        - Bank account reconciliation history\n        - Bank statement matching records\n        - Bank account verification records\n        \n        Do NOT use this tool when the user is asking about a specific bank reconciliation by ID.\n        \n        Args:\n            updated_or_created_since: Optional limit to Bank Reconciliations changed since a given date\n                                    (format: YYYY-MM-DDT(+|-)hh:mm) or date-time\n                                    (format: YYYY-MM-DDThh:mm:ss(+|-)hh:mm)\n            bank_account_id: Optional filter by bank account id\n            items_per_page: Number of items to return per page (default: 20, max: 200)\n            page: Page number to retrieve (default: 1)\n            attributes: Optional attributes to expose for bank reconciliations\n            \n        Returns:\n            Dictionary containing the list of bank reconciliations and metadata\n        ", "parameters": {"properties": {"updated_or_created_since": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Updated Or Created Since"}, "bank_account_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Bank Account Id"}, "items_per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 20, "title": "Items Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "title": "get_bank_reconciliationsArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863719+00:00"}, {"name": "get_bank_reconciliation_by_id", "description": "Returns details for a specific Bank Reconciliation.\n        \n        This tool retrieves detailed information for a specific bank reconciliation\n        from Sage Business Cloud Accounting.\n        \n        Use this tool when the user asks about:\n        - Details of a specific bank reconciliation\n        - Information about a bank reconciliation by ID\n        - Specific bank statement reconciliation details\n        \n        Do NOT use this tool for listing multiple bank reconciliations.\n        \n        Args:\n            key: The Bank Reconciliation Key (ID)\n            attributes: Optional attributes to expose for the bank reconciliation\n            \n        Returns:\n            Dictionary containing detailed bank reconciliation information\n        ", "parameters": {"properties": {"key": {"title": "Key", "type": "string"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "required": ["key"], "title": "get_bank_reconciliation_by_idArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863726+00:00"}, {"name": "get_transactions", "description": "Returns all Transactions.\n        \n        This tool retrieves transaction data from Sage Business Cloud Accounting.\n        \n        Use this tool when the user asks about:\n        - List of all transactions\n        - Financial events recorded in the system\n        - Transaction history within a date range\n        - Transactions of a specific type\n        \n        Args:\n            updated_or_created_since: Optional limit to transactions changed since a given date (ISO format)\n            from_date: Optional start date for transactions (ISO format)\n            to_date: Optional end date for transactions (ISO format)\n            updated_from_date: Optional filter by earliest update date (ISO format)\n            updated_to_date: Optional filter by latest update date (ISO format)\n            has_attachments: Optional filter for transactions with attachments\n            items_per_page: Number of items per page (default: 20, max: 200)\n            page: Page number to retrieve (default: 1)\n            attributes: Optional attributes to expose for the transactions\n            transaction_type_id: Optional filter by transaction type ID\n            \n        Returns:\n            Dictionary containing the list of transactions and metadata\n        ", "parameters": {"properties": {"updated_or_created_since": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Updated Or Created Since"}, "from_date": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "From Date"}, "to_date": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "To Date"}, "updated_from_date": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Updated From Date"}, "updated_to_date": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Updated To Date"}, "has_attachments": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Has Attachments"}, "items_per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 20, "title": "Items Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}, "transaction_type_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Transaction Type Id"}}, "title": "get_transactionsArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863733+00:00"}, {"name": "get_transaction_by_id", "description": "Returns a Transaction.\n        \n        This tool retrieves detailed information for a specific transaction\n        from Sage Business Cloud Accounting.\n        \n        Use this tool when the user asks about:\n        - Details of a specific transaction\n        - Information about a transaction by ID\n        - The source or origin of a particular transaction\n        \n        Args:\n            key: The Transaction Key (ID)\n            expand_origin: Optional flag to expand origin information\n            attributes: Optional attributes to expose for the transaction\n            \n        Returns:\n            Dictionary containing detailed transaction information\n        ", "parameters": {"properties": {"key": {"title": "Key", "type": "string"}, "expand_origin": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Expand Origin"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "required": ["key"], "title": "get_transaction_by_idArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863739+00:00"}, {"name": "get_transaction_types", "description": "Returns all Transaction Types.\n        \n        This tool retrieves transaction type data from Sage Business Cloud Accounting.\n        Transaction types are used to categorize transactions by their business purpose.\n        \n        Use this tool when the user asks about:\n        - Types of transactions available in the system\n        - Categories of financial events\n        - Valid transaction types for creating transactions\n        \n        Args:\n            valid_for_business: Optional filter for types valid for the current business\n            items_per_page: Number of items per page (default: 20, max: 200)\n            page: Page number to retrieve (default: 1)\n            attributes: Optional attributes to expose for the transaction types\n            \n        Returns:\n            Dictionary containing the list of transaction types and metadata\n        ", "parameters": {"properties": {"valid_for_business": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Valid For Business"}, "items_per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 20, "title": "Items Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "title": "get_transaction_typesArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863744+00:00"}, {"name": "get_transaction_type_by_id", "description": "Returns a Transaction Type.\n        \n        This tool retrieves detailed information for a specific transaction type\n        from Sage Business Cloud Accounting.\n        \n        Use this tool when the user asks about:\n        - Details of a specific transaction type\n        - Information about a transaction type by ID\n        - Whether a particular transaction type is valid for use\n        \n        Args:\n            key: The Transaction Type Key (ID)\n            attributes: Optional attributes to expose for the transaction type\n            \n        Returns:\n            Dictionary containing detailed transaction type information\n        ", "parameters": {"properties": {"key": {"title": "Key", "type": "string"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "required": ["key"], "title": "get_transaction_type_by_idArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863749+00:00"}, {"name": "get_ledger_entries", "description": "Returns all Ledger Entries.\n        \n        This tool retrieves ledger entry data from Sage Business Cloud Accounting.\n        \n        Use this tool when the user asks about:\n        - List of all ledger entries\n        - Movements on ledger accounts\n        - Credit and debit entries in the system\n        - Specific accounting entries by date, transaction, or account\n        \n        Args:\n            from_date: Optional start date for ledger entries (ISO format)\n            to_date: Optional end date for ledger entries (ISO format)\n            transaction_id: Optional filter by transaction ID\n            transaction_type_id: Optional filter by transaction type ID\n            journal_code_id: Optional filter by journal code ID\n            updated_or_created_since: Optional limit to entries changed since a given date (ISO format)\n            items_per_page: Number of items per page (default: 20, max: 200)\n            page: Page number to retrieve (default: 1)\n            attributes: Optional attributes to expose for the ledger entries\n            ledger_account_id: Optional filter by ledger account ID\n            \n        Returns:\n            Dictionary containing the list of ledger entries and metadata\n        ", "parameters": {"properties": {"from_date": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "From Date"}, "to_date": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "To Date"}, "transaction_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Transaction Id"}, "transaction_type_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Transaction Type Id"}, "journal_code_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Journal Code Id"}, "updated_or_created_since": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Updated Or Created Since"}, "items_per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 20, "title": "Items Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}, "ledger_account_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Ledger Account Id"}}, "title": "get_ledger_entriesArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863755+00:00"}, {"name": "get_ledger_entry_by_id", "description": "Returns a Ledger Entry.\n        \n        This tool retrieves detailed information for a specific ledger entry\n        from Sage Business Cloud Accounting.\n        \n        Use this tool when the user asks about:\n        - Details of a specific ledger entry\n        - Information about a ledger entry by ID\n        - Specific accounting entry details\n        \n        Args:\n            key: The Ledger Entry Key (ID)\n            nested_attributes: Optional attributes to expose for nested entities\n            attributes: Optional attributes to expose for the ledger entry\n            \n        Returns:\n            Dictionary containing detailed ledger entry information\n        ", "parameters": {"properties": {"key": {"title": "Key", "type": "string"}, "nested_attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Nested Attributes"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "required": ["key"], "title": "get_ledger_entry_by_idArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863761+00:00"}, {"name": "get_journals", "description": "Returns all Journals.\n        \n        This tool retrieves journal data from Sage Business Cloud Accounting.\n        \n        Use this tool when the user asks about:\n        - List of all journals\n        - Journal entries for recording non-regular transactions\n        - Finding journals by various attributes\n        \n        Args:\n            updated_or_created_since: Optional limit to journals changed since a given date (ISO format)\n            deleted_since: Optional limit to journals deleted since a given date (ISO format)\n            items_per_page: Number of items per page (default: 20, max: 200)\n            page: Page number to retrieve (default: 1)\n            attributes: Optional attributes to expose for the journals\n            \n        Returns:\n            Dictionary containing the list of journals and metadata\n        ", "parameters": {"properties": {"updated_or_created_since": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Updated Or Created Since"}, "deleted_since": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Deleted Since"}, "items_per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 20, "title": "Items Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "title": "get_journalsArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863766+00:00"}, {"name": "get_journal_by_id", "description": "Returns a Journal.\n        \n        This tool retrieves detailed information for a specific journal\n        from Sage Business Cloud Accounting.\n        \n        Use this tool when the user asks about:\n        - Details of a specific journal\n        - Information about a journal by ID\n        - Line items on a particular journal\n        \n        Args:\n            key: The Journal Key (ID)\n            nested_attributes: Optional attributes to expose for nested entities\n            show_analysis_types: Optional flag to show line item analysis types\n            attributes: Optional attributes to expose for the journal\n            \n        Returns:\n            Dictionary containing detailed journal information\n        ", "parameters": {"properties": {"key": {"title": "Key", "type": "string"}, "nested_attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Nested Attributes"}, "show_analysis_types": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Show Analysis Types"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "required": ["key"], "title": "get_journal_by_idArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863772+00:00"}, {"name": "get_journal_codes", "description": "Returns all Journal Codes.\n        \n        This tool retrieves journal code data from Sage Business Cloud Accounting.\n        Journal codes are used to classify journal entries in French businesses.\n        \n        Use this tool when the user asks about:\n        - List of all journal codes\n        - Journal classification codes\n        - Auxiliary journal codes\n        \n        Args:\n            updated_or_created_since: Optional limit to journal codes changed since a given date (ISO format)\n            items_per_page: Number of items per page (default: 20, max: 200)\n            page: Page number to retrieve (default: 1)\n            attributes: Optional attributes to expose for the journal codes\n            \n        Returns:\n            Dictionary containing the list of journal codes and metadata\n        ", "parameters": {"properties": {"updated_or_created_since": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Updated Or Created Since"}, "items_per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 20, "title": "Items Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "title": "get_journal_codesArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863778+00:00"}, {"name": "get_journal_code_by_id", "description": "Returns a Journal Code.\n        \n        This tool retrieves detailed information for a specific journal code\n        from Sage Business Cloud Accounting.\n        \n        Use this tool when the user asks about:\n        - Details of a specific journal code\n        - Information about a journal code by ID\n        \n        Args:\n            key: The Journal Code Key (ID)\n            attributes: Optional attributes to expose for the journal code\n            \n        Returns:\n            Dictionary containing detailed journal code information\n        ", "parameters": {"properties": {"key": {"title": "Key", "type": "string"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "required": ["key"], "title": "get_journal_code_by_idArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863783+00:00"}, {"name": "get_journal_code_types", "description": "Returns all Journal Code Types.\n        \n        This tool retrieves journal code type data from Sage Business Cloud Accounting.\n        Journal code types are used to categorize journal codes in French businesses.\n        \n        Use this tool when the user asks about:\n        - Types of journal codes in the system\n        - Categories of journal codes\n        - Journal code classification types\n        \n        Args:\n            items_per_page: Number of items per page (default: 20, max: 200)\n            page: Page number to retrieve (default: 1)\n            attributes: Optional attributes to expose for the journal code types\n            \n        Returns:\n            Dictionary containing the list of journal code types and metadata\n        ", "parameters": {"properties": {"items_per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 20, "title": "Items Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "title": "get_journal_code_typesArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863789+00:00"}, {"name": "get_journal_code_type_by_id", "description": "Returns a Journal Code Type.\n        \n        This tool retrieves detailed information for a specific journal code type\n        from Sage Business Cloud Accounting.\n        \n        Use this tool when the user asks about:\n        - Details of a specific journal code type\n        - Information about a journal code type by ID\n        \n        Args:\n            key: The Journal Code Type Key (ID)\n            attributes: Optional attributes to expose for the journal code type\n            \n        Returns:\n            Dictionary containing detailed journal code type information\n        ", "parameters": {"properties": {"key": {"title": "Key", "type": "string"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "required": ["key"], "title": "get_journal_code_type_by_idArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863796+00:00"}, {"name": "get_coa_accounts", "description": "Returns all Coa Accounts\n\n        ### Endpoint Availability\n\n        * Accounting Plus: 🇨🇦, 🇪🇸, 🇫🇷, 🇬🇧, 🇮🇪, 🇺🇸\n        * Accounting Standard: 🇬🇧, 🇮🇪\n        * Accounting Start: 🇨🇦, 🇪🇸, 🇫🇷, 🇬🇧, 🇮🇪, 🇺🇸\n        \n        Args:\n            coa_template_id: Use this to filter by COA Template\n            updated_or_created_since: Use this to limit the response to Coa Accounts changed since a given date (format: YYYY-MM-DDT(+|-)hh:mm) or date-time (format: YYYY-MM-DDThh:mm:ss(+|-)hh:mm). Inclusive of the passed timestamp.\n            items_per_page: Returns the given number of Coa Accounts per request.\n            page: Go to specific page of Coa Accounts\n            attributes: Specify the attributes that you want to expose for the Coa Accounts (expose all attributes with 'all'). These are in addition to the base attributes (name, path)\n            \n        Returns:\n            Dictionary containing the list of coa accounts and metadata\n        ", "parameters": {"properties": {"coa_template_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Coa Template Id"}, "updated_or_created_since": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Updated Or Created Since"}, "items_per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 20, "title": "Items Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "title": "get_coa_accountsArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863802+00:00"}, {"name": "get_coa_account_by_id", "description": "Returns a Coa Account\n\n        ### Endpoint Availability\n\n        * Accounting Plus: 🇨🇦, 🇪🇸, 🇫🇷, 🇬🇧, 🇮🇪, 🇺🇸\n        * Accounting Standard: 🇬🇧, 🇮🇪\n        * Accounting Start: 🇨🇦, 🇪🇸, 🇫🇷, 🇬🇧, 🇮🇪, 🇺🇸\n        \n        Args:\n            key: The Coa Account Key.\n            attributes: Specify the attributes that you want to expose for the Coa Account (expose all attributes with 'all'). These are in addition to the base attributes (name, path)\n            \n        Returns:\n            Dictionary containing detailed coa account information\n        ", "parameters": {"properties": {"key": {"title": "Key", "type": "string"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "required": ["key"], "title": "get_coa_account_by_idArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863808+00:00"}, {"name": "get_coa_templates", "description": "Returns all Coa Templates\n\n        ### Endpoint Availability\n\n        * Accounting Plus: 🇨🇦, 🇪🇸, 🇫🇷, 🇬🇧, 🇮🇪, 🇺🇸\n        * Accounting Standard: 🇬🇧, 🇮🇪\n        * Accounting Start: 🇨🇦, 🇪🇸, 🇫🇷, 🇬🇧, 🇮🇪, 🇺🇸\n        \n        Args:\n            country_id: Use this to filter by country id\n            items_per_page: Returns the given number of Coa Templates per request.\n            page: Go to specific page of Coa Templates\n            attributes: Specify the attributes that you want to expose for the Coa Templates (expose all attributes with 'all'). These are in addition to the base attributes (name, path)\n            \n        Returns:\n            Dictionary containing the list of coa templates and metadata\n        ", "parameters": {"properties": {"country_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Country Id"}, "items_per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 20, "title": "Items Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "title": "get_coa_templatesArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863813+00:00"}, {"name": "get_coa_template_by_id", "description": "Returns a Coa Template\n\n        ### Endpoint Availability\n\n        * Accounting Plus: 🇨🇦, 🇪🇸, 🇫🇷, 🇬🇧, 🇮🇪, 🇺🇸\n        * Accounting Standard: 🇬🇧, 🇮🇪\n        * Accounting Start: 🇨🇦, 🇪🇸, 🇫🇷, 🇬🇧, 🇮🇪, 🇺🇸\n        \n        Args:\n            key: The Coa Template Key.\n            attributes: Specify the attributes that you want to expose for the Coa Template (expose all attributes with 'all'). These are in addition to the base attributes (name, path)\n            \n        Returns:\n            Dictionary containing detailed coa template information\n        ", "parameters": {"properties": {"key": {"title": "Key", "type": "string"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "required": ["key"], "title": "get_coa_template_by_idArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863819+00:00"}, {"name": "get_ledger_accounts", "description": "Returns all Ledger Accounts\n\n        ### Endpoint Availability\n\n        * Accounting Plus: 🇨🇦, 🇪🇸, 🇫🇷, 🇬🇧, 🇮🇪, 🇺🇸\n        * Accounting Standard: 🇬🇧, 🇮🇪\n        * Accounting Start: 🇨🇦, 🇪🇸, 🇫🇷, 🇬🇧, 🇮🇪, 🇺🇸\n\n        ### Access Control Restrictions\n\n        Requires the authenticated user to have any mentioned role in one of the listed areas:\n        * Area: `Sales`: Full Access, Restricted Access, Read Only\n        * Area: `Purchases`: Full Access, Restricted Access, Read Only\n        * Area: `Products & Services`: Full Access, Restricted Access, Read Only\n        * Area: `Contacts`: Full Access, Restricted Access, Read Only\n        * Area: `Bank`: Full Access, Restricted Access, Read Only\n        * Area: `Journals`: Full Access, Restricted Access, Read Only\n        * Area: `Settings`: Full Access, Read Only\n        \n        Args:\n            updated_or_created_since: Use this to limit the response to Ledger Accounts changed since a given date (format: YYYY-MM-DDT(+|-)hh:mm) or date-time (format: YYYY-MM-DDThh:mm:ss(+|-)hh:mm). Inclusive of the passed timestamp.\n            visible_in: Use this to limit the response to ledger account types visible in a specific area. Valid values are: banking, sales, expenses, other_payments, other_receipts, journals and reporting\n            not_visible_in: Use this to limit the response to ledger account types not visible in a specific area.\n            show_included_in_chart: Use this to limit the response to ledger accounts that are/are not included in the chart\n            show_control_accounts: Use this to limit the response to ledger accounts that are/are not control accounts\n            ledger_account_classification_id: Use this to filter by ledger account classification id\n            show_balance_details: Use this to display the balance details for ledger accounts between a date range (requires from_date and to_date).\n            exclude_deleted_entries: Exclude deleted ledger entries.\n            from_date: Calculate balances from this date.\n            to_date: Calculate balances to this date.\n            search: Use this to filter by the item code or description\n            sort_order_from_user_setting: Use this to enable ordering ledger accounts according to user settings. Defaulted to 'false'.\n            filter_inactive_bank_accounts: Use this to filter inactive bank accounts\n            items_per_page: Returns the given number of Ledger Accounts per request.\n            page: Go to specific page of Ledger Accounts\n            attributes: Specify the attributes that you want to expose for the Ledger Accounts (expose all attributes with 'all'). These are in addition to the base attributes (name, path)\n            ledger_account_type_id: Use this to filter by ledger account type id\n            \n        Returns:\n            Dictionary containing the list of ledger accounts and metadata\n        ", "parameters": {"properties": {"updated_or_created_since": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Updated Or Created Since"}, "visible_in": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Visible In"}, "not_visible_in": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Not Visible In"}, "show_included_in_chart": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Show Included In Chart"}, "show_control_accounts": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Show Control Accounts"}, "ledger_account_classification_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Ledger Account Classification Id"}, "show_balance_details": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Show Balance Details"}, "exclude_deleted_entries": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Exclude Deleted Entries"}, "from_date": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "From Date"}, "to_date": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "To Date"}, "search": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Search"}, "sort_order_from_user_setting": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Sort Order From User Setting"}, "filter_inactive_bank_accounts": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Filter Inactive Bank Accounts"}, "items_per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 20, "title": "Items Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}, "ledger_account_type_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Ledger Account Type Id"}}, "title": "get_ledger_accountsArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863825+00:00"}, {"name": "get_ledger_account_by_id", "description": "Returns a Ledger Account\n\n        ### Endpoint Availability\n\n        * Accounting Plus: 🇨🇦, 🇪🇸, 🇫🇷, 🇬🇧, 🇮🇪, 🇺🇸\n        * Accounting Standard: 🇬🇧, 🇮🇪\n        * Accounting Start: 🇨🇦, 🇪🇸, 🇫🇷, 🇬🇧, 🇮🇪, 🇺🇸\n\n        ### Access Control Restrictions\n\n        Requires the authenticated user to have any mentioned role in one of the listed areas:\n        * Area: `Sales`: Full Access, Restricted Access, Read Only\n        * Area: `Purchases`: Full Access, Restricted Access, Read Only\n        * Area: `Products & Services`: Full Access, Restricted Access, Read Only\n        * Area: `Contacts`: Full Access, Restricted Access, Read Only\n        * Area: `Bank`: Full Access, Restricted Access, Read Only\n        * Area: `Journals`: Full Access, Restricted Access, Read Only\n        * Area: `Settings`: Full Access, Read Only\n        \n        Args:\n            key: The Ledger Account Key.\n            nested_attributes: Specify the attributes that you want to expose for nested entities of the Ledger Account (expose all nested attributes with 'all'). These are in addition to the base attributes (name, path)\n            show_balance_details: Use this to display the balance details for ledger accounts between a date range (requires from_date and to_date).\n            exclude_deleted_entries: Exclude deleted ledger entries.\n            from_date: Calculate balances from this date.\n            to_date: Calculate balances to this date.\n            attributes: Specify the attributes that you want to expose for the Ledger Account (expose all attributes with 'all'). These are in addition to the base attributes (name, path)\n            \n        Returns:\n            Dictionary containing detailed ledger account information\n        ", "parameters": {"properties": {"key": {"title": "Key", "type": "string"}, "nested_attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Nested Attributes"}, "show_balance_details": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Show Balance Details"}, "exclude_deleted_entries": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Exclude Deleted Entries"}, "from_date": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "From Date"}, "to_date": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "To Date"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "required": ["key"], "title": "get_ledger_account_by_idArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863831+00:00"}, {"name": "create_ledger_account", "description": "Creates a Ledger Account\n\n        ### Endpoint Availability\n\n        * Accounting Plus: 🇨🇦, 🇫🇷, 🇬🇧, 🇮🇪, 🇺🇸\n        * Accounting Standard: 🇨🇦, 🇫🇷, 🇬🇧, 🇮🇪, 🇺🇸\n        * Accounting Start: 🇨🇦, 🇫🇷, 🇬🇧, 🇮🇪, 🇺🇸\n\n        ### Access Control Restrictions\n\n        Requires the authenticated user to have any mentioned role in one of the listed areas:\n        * Area: `Settings`: Full Access\n        \n        Args:\n            ledger_account_type_id: The ledger account type for the ledger account\n            included_in_chart: Indicates whether the ledger account is included in the chart of accounts\n            name: The name for the ledger account\n            display_name: The display name for the ledger account\n            nominal_code: The nominal code of the ledger account (between 1 and ********)\n            ledger_account_classification_id: The ID of the Ledger Account Classification\n            tax_rate_id: The ID of the Tax Rate\n            fixed_tax_rate: Indicates whether the default tax rate is fixed or may be changed per transaction\n            visible_in_banking: Indicates whether the ledger account is displayed in the banking area of the application\n            visible_in_expenses: Indicates whether the ledger account is displayed in the purchases area of the application\n            visible_in_journals: Indicates whether the ledger account is displayed in the journals area of the application\n            visible_in_other_payments: Indicates whether the ledger account is displayed in the other payments area of the application\n            visible_in_other_receipts: Indicates whether the ledger account is displayed in the other receipts area of the application\n            visible_in_reporting: Indicates whether the ledger account is displayed in the reporting area of the application\n            visible_in_sales: Indicates whether the ledger account is displayed in the sales area of the application\n            control_name: The control name for the ledger account\n            tax_recoverable: Indicates that transactions posted to this ledger account have part recoverable taxes (Canada only)\n            recoverable_percentage: The partial recoverable tax rate (Canada only)\n            non_recoverable_ledger_account_id: The ID of the Non Recoverable Ledger Account\n            cis_materials: Indicates whether the ledger account is flagged for CIS Materials\n            cis_labour: Indicates whether the ledger account is flagged for CIS Labour\n            gifi_code: The GIFI code of the ledger account (Canada only, between 1000 and 9999)\n            \n        Returns:\n            Dictionary containing the created ledger account\n        ", "parameters": {"properties": {"ledger_account_type_id": {"title": "Ledger Account Type Id", "type": "string"}, "included_in_chart": {"title": "Included In Chart", "type": "boolean"}, "name": {"title": "Name", "type": "string"}, "display_name": {"title": "Display Name", "type": "string"}, "nominal_code": {"title": "Nominal Code", "type": "integer"}, "ledger_account_classification_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Ledger Account Classification Id"}, "tax_rate_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Tax Rate Id"}, "fixed_tax_rate": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Fixed Tax Rate"}, "visible_in_banking": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Visible In Banking"}, "visible_in_expenses": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Visible In Expenses"}, "visible_in_journals": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Visible In Journals"}, "visible_in_other_payments": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Visible In Other Payments"}, "visible_in_other_receipts": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Visible In Other Receipts"}, "visible_in_reporting": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Visible In Reporting"}, "visible_in_sales": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Visible In Sales"}, "control_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Control Name"}, "tax_recoverable": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Tax Recoverable"}, "recoverable_percentage": {"anyOf": [{"type": "number"}, {"type": "null"}], "default": null, "title": "Recoverable Percentage"}, "non_recoverable_ledger_account_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Non Recoverable Ledger Account Id"}, "cis_materials": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Cis Materials"}, "cis_labour": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Cis Labour"}, "gifi_code": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Gifi Code"}}, "required": ["ledger_account_type_id", "included_in_chart", "name", "display_name", "nominal_code"], "title": "create_ledger_accountArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863837+00:00"}, {"name": "update_ledger_account", "description": "Updates a Ledger Account\n\n        ### Endpoint Availability\n\n        * Accounting Plus: 🇨🇦, 🇪🇸, 🇫🇷, 🇬🇧, 🇮🇪, 🇺🇸\n        * Accounting Standard: 🇬🇧, 🇮🇪\n        * Accounting Start: 🇨🇦, 🇪🇸, 🇫🇷, 🇬🇧, 🇮🇪, 🇺🇸\n\n        ### Access Control Restrictions\n\n        Requires the authenticated user to have any mentioned role in one of the listed areas:\n        * Area: `Settings`: Full Access\n        \n        Args:\n            key: The Ledger Account Key\n            ledger_account_type_id: The ledger account type for the ledger account\n            included_in_chart: Indicates whether the ledger account is included in the chart of accounts\n            name: The name for the ledger account\n            display_name: The display name for the ledger account\n            nominal_code: The nominal code of the ledger account (between 1 and ********)\n            ledger_account_classification_id: The ID of the Ledger Account Classification\n            tax_rate_id: The ID of the Tax Rate\n            fixed_tax_rate: Indicates whether the default tax rate is fixed or may be changed per transaction\n            visible_in_banking: Indicates whether the ledger account is displayed in the banking area of the application\n            visible_in_expenses: Indicates whether the ledger account is displayed in the purchases area of the application\n            visible_in_journals: Indicates whether the ledger account is displayed in the journals area of the application\n            visible_in_other_payments: Indicates whether the ledger account is displayed in the other payments area of the application\n            visible_in_other_receipts: Indicates whether the ledger account is displayed in the other receipts area of the application\n            visible_in_reporting: Indicates whether the ledger account is displayed in the reporting area of the application\n            visible_in_sales: Indicates whether the ledger account is displayed in the sales area of the application\n            control_name: The control name for the ledger account\n            tax_recoverable: Indicates that transactions posted to this ledger account have part recoverable taxes (Canada only)\n            recoverable_percentage: The partial recoverable tax rate (Canada only)\n            non_recoverable_ledger_account_id: The ID of the Non Recoverable Ledger Account\n            cis_materials: Indicates whether the ledger account is flagged for CIS Materials\n            cis_labour: Indicates whether the ledger account is flagged for CIS Labour\n            gifi_code: The GIFI code of the ledger account (Canada only, between 1000 and 9999)\n            \n        Returns:\n            Dictionary containing the updated ledger account\n        ", "parameters": {"properties": {"key": {"title": "Key", "type": "string"}, "ledger_account_type_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Ledger Account Type Id"}, "included_in_chart": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Included In Chart"}, "name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Name"}, "display_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Display Name"}, "nominal_code": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Nominal Code"}, "ledger_account_classification_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Ledger Account Classification Id"}, "tax_rate_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Tax Rate Id"}, "fixed_tax_rate": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Fixed Tax Rate"}, "visible_in_banking": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Visible In Banking"}, "visible_in_expenses": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Visible In Expenses"}, "visible_in_journals": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Visible In Journals"}, "visible_in_other_payments": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Visible In Other Payments"}, "visible_in_other_receipts": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Visible In Other Receipts"}, "visible_in_reporting": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Visible In Reporting"}, "visible_in_sales": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Visible In Sales"}, "control_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Control Name"}, "tax_recoverable": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Tax Recoverable"}, "recoverable_percentage": {"anyOf": [{"type": "number"}, {"type": "null"}], "default": null, "title": "Recoverable Percentage"}, "non_recoverable_ledger_account_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Non Recoverable Ledger Account Id"}, "cis_materials": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Cis Materials"}, "cis_labour": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Cis Labour"}, "gifi_code": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Gifi Code"}}, "required": ["key"], "title": "update_ledger_accountArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863843+00:00"}, {"name": "get_ledger_account_classifications", "description": "Returns all Ledger Account Classifications\n\n        ### Endpoint Availability\n\n        * Accounting Plus: 🇨🇦, 🇪🇸, 🇫🇷, 🇬🇧, 🇮🇪, 🇺🇸\n        * Accounting Standard: 🇬🇧, 🇮🇪\n        * Accounting Start: 🇨🇦, 🇪🇸, 🇫🇷, 🇬🇧, 🇮🇪, 🇺🇸\n        \n        Args:\n            ledger_account_type_id: Use this to filter by ledger account type id\n            items_per_page: Returns the given number of Ledger Account Classifications per request.\n            page: Go to specific page of Ledger Account Classifications\n            attributes: Specify the attributes that you want to expose for the Ledger Account Classifications (expose all attributes with 'all'). These are in addition to the base attributes (name, path)\n            \n        Returns:\n            Dictionary containing the list of ledger account classifications and metadata\n        ", "parameters": {"properties": {"ledger_account_type_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Ledger Account Type Id"}, "items_per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 20, "title": "Items Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "title": "get_ledger_account_classificationsArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863849+00:00"}, {"name": "get_ledger_account_classification_by_id", "description": "Returns a Ledger Account Classification\n\n        ### Endpoint Availability\n\n        * Accounting Plus: 🇨🇦, 🇪🇸, 🇫🇷, 🇬🇧, 🇮🇪, 🇺🇸\n        * Accounting Standard: 🇬🇧, 🇮🇪\n        * Accounting Start: 🇨🇦, 🇪🇸, 🇫🇷, 🇬🇧, 🇮🇪, 🇺🇸\n        \n        Args:\n            key: The Ledger Account Classification Key.\n            attributes: Specify the attributes that you want to expose for the Ledger Account Classification (expose all attributes with 'all'). These are in addition to the base attributes (name, path)\n            \n        Returns:\n            Dictionary containing detailed ledger account classification information\n        ", "parameters": {"properties": {"key": {"title": "Key", "type": "string"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "required": ["key"], "title": "get_ledger_account_classification_by_idArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863854+00:00"}, {"name": "get_ledger_account_types", "description": "Returns all Ledger Account Types\n\n        ### Endpoint Availability\n\n        * Accounting Plus: 🇨🇦, 🇪🇸, 🇫🇷, 🇬🇧, 🇮🇪, 🇺🇸\n        * Accounting Standard: 🇬🇧, 🇮🇪\n        * Accounting Start: 🇨🇦, 🇪🇸, 🇫🇷, 🇬🇧, 🇮🇪, 🇺🇸\n        \n        Args:\n            ledger_account_classification_id: Use this to filter by ledger account classification id\n            items_per_page: Returns the given number of Ledger Account Types per request.\n            page: Go to specific page of Ledger Account Types\n            attributes: Specify the attributes that you want to expose for the Ledger Account Types (expose all attributes with 'all'). These are in addition to the base attributes (name, path)\n            \n        Returns:\n            Dictionary containing the list of ledger account types and metadata\n        ", "parameters": {"properties": {"ledger_account_classification_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Ledger Account Classification Id"}, "items_per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 20, "title": "Items Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "title": "get_ledger_account_typesArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863867+00:00"}, {"name": "get_ledger_account_type_by_id", "description": "Returns a Ledger Account Type\n\n        ### Endpoint Availability\n\n        * Accounting Plus: 🇨🇦, 🇪🇸, 🇫🇷, 🇬🇧, 🇮🇪, 🇺🇸\n        * Accounting Standard: 🇬🇧, 🇮🇪\n        * Accounting Start: 🇨🇦, 🇪🇸, 🇫🇷, 🇬🇧, 🇮🇪, 🇺🇸\n        \n        Args:\n            key: The Ledger Account Type Key.\n            attributes: Specify the attributes that you want to expose for the Ledger Account Type (expose all attributes with 'all'). These are in addition to the base attributes (name, path)\n            \n        Returns:\n            Dictionary containing detailed ledger account type information\n        ", "parameters": {"properties": {"key": {"title": "Key", "type": "string"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "required": ["key"], "title": "get_ledger_account_type_by_idArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863876+00:00"}, {"name": "get_tax_rates", "description": "Returns all Tax Rates.\n        \n        This tool retrieves tax rate data from Sage Business Cloud Accounting.\n        \n        Use this tool when the user asks about:\n        - List of all tax rates\n        - VAT rates available in the system\n        - Tax percentages for different regions\n        - Historical tax rate changes\n        \n        Args:\n            updated_or_created_since: Optional limit to tax rates changed since a given date (ISO format)\n            usage: Optional filter for sales or purchase usage (values: 'sales', 'purchase')\n            address_region_id: Optional filter by address region ID\n            date: Optional date to return tax rates applicable on that date (ISO format)\n            include_historical_data: Optional flag to include historical tax rate data\n            include_destination_vat_rates: Optional flag to include VAT destination tax rates\n            cis_only: Optional filter for Construction Industry Scheme (CIS) tax rates only\n            items_per_page: Number of items per page (default: 20, max: 200)\n            page: Page number to retrieve (default: 1)\n            attributes: Optional attributes to expose for the tax rates\n            \n        Returns:\n            Dictionary containing the list of tax rates and metadata\n        ", "parameters": {"properties": {"updated_or_created_since": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Updated Or Created Since"}, "usage": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Usage"}, "address_region_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Address Region Id"}, "date": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Date"}, "include_historical_data": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Include Historical Data"}, "include_destination_vat_rates": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Include Destination Vat Rates"}, "cis_only": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Cis Only"}, "items_per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 20, "title": "Items Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "title": "get_tax_ratesArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863882+00:00"}, {"name": "get_tax_rate_by_id", "description": "Returns a Tax Rate.\n        \n        This tool retrieves detailed information for a specific tax rate\n        from Sage Business Cloud Accounting.\n        \n        Use this tool when the user asks about:\n        - Details of a specific tax rate\n        - Information about a tax rate by ID\n        - Current percentage for a particular tax rate\n        - Historical changes to a tax rate\n        \n        Args:\n            key: The Tax Rate Key (ID)\n            date: Optional date to show the tax rate percentage on that date (ISO format)\n            attributes: Optional attributes to expose for the tax rate\n            \n        Returns:\n            Dictionary containing detailed tax rate information\n        ", "parameters": {"properties": {"key": {"title": "Key", "type": "string"}, "date": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Date"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "required": ["key"], "title": "get_tax_rate_by_idArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863888+00:00"}, {"name": "get_tax_profiles", "description": "Returns all Tax Profiles.\n        \n        This tool retrieves tax profile data from Sage Business Cloud Accounting.\n        Tax profiles hold details about the current business' VAT taxation.\n        \n        Use this tool when the user asks about:\n        - List of all tax profiles\n        - Business tax registration information\n        - VAT taxation details for the business\n        - Tax return filing frequencies\n        \n        Args:\n            updated_or_created_since: Optional limit to tax profiles changed since a given date (ISO format)\n            items_per_page: Number of items per page (default: 20, max: 200)\n            page: Page number to retrieve (default: 1)\n            attributes: Optional attributes to expose for the tax profiles\n            \n        Returns:\n            Dictionary containing the list of tax profiles and metadata\n        ", "parameters": {"properties": {"updated_or_created_since": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Updated Or Created Since"}, "items_per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 20, "title": "Items Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "title": "get_tax_profilesArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863894+00:00"}, {"name": "get_tax_profile_by_id", "description": "Returns a Tax Profile.\n        \n        This tool retrieves detailed information for a specific tax profile\n        from Sage Business Cloud Accounting.\n        \n        Use this tool when the user asks about:\n        - Details of a specific tax profile\n        - Information about a tax profile by ID\n        - Tax registration details for a specific tax type\n        - Tax return filing frequency for a specific tax\n        \n        Args:\n            key: The Tax Profile Key (ID)\n            attributes: Optional attributes to expose for the tax profile\n            \n        Returns:\n            Dictionary containing detailed tax profile information\n        ", "parameters": {"properties": {"key": {"title": "Key", "type": "string"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "required": ["key"], "title": "get_tax_profile_by_idArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863899+00:00"}, {"name": "get_tax_types", "description": "Returns all Tax Types.\n        \n        This tool retrieves tax type data from Sage Business Cloud Accounting.\n        Tax types represent different ways to report taxes in various countries.\n        \n        Use this tool when the user asks about:\n        - List of all tax types\n        - Types of taxes that can be filed\n        - Tax reporting options available\n        - Country-specific tax types\n        \n        Args:\n            items_per_page: Number of items per page (default: 20, max: 200)\n            page: Page number to retrieve (default: 1)\n            attributes: Optional attributes to expose for the tax types\n            \n        Returns:\n            Dictionary containing the list of tax types and metadata\n        ", "parameters": {"properties": {"items_per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 20, "title": "Items Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "title": "get_tax_typesArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863905+00:00"}, {"name": "get_tax_type_by_id", "description": "Returns a Tax Type.\n        \n        This tool retrieves detailed information for a specific tax type\n        from Sage Business Cloud Accounting.\n        \n        Use this tool when the user asks about:\n        - Details of a specific tax type\n        - Information about a tax type by ID\n        - Whether a tax type is federal or regional\n        - Which country a specific tax type applies to\n        - Which tax rates are associated with a specific tax type\n        \n        Args:\n            key: The Tax Type Key (ID)\n            attributes: Optional attributes to expose for the tax type\n            \n        Returns:\n            Dictionary containing detailed tax type information\n        ", "parameters": {"properties": {"key": {"title": "Key", "type": "string"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "required": ["key"], "title": "get_tax_type_by_idArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863911+00:00"}, {"name": "get_tax_return_frequencies", "description": "Returns all Tax Return Frequencies.\n        \n        This tool retrieves tax return frequency data from Sage Business Cloud Accounting.\n        Tax return frequencies define how often tax returns must be filed (monthly, quarterly, annually).\n        \n        Use this tool when the user asks about:\n        - List of all tax return frequencies\n        - How often tax returns can be filed\n        - Available filing frequencies for taxes\n        - Options for tax return submission frequency\n        \n        Args:\n            tax_type_id: Optional filter by tax type ID (Canada only)\n            items_per_page: Number of items per page (default: 20, max: 200)\n            page: Page number to retrieve (default: 1)\n            attributes: Optional attributes to expose for the tax return frequencies\n            \n        Returns:\n            Dictionary containing the list of tax return frequencies and metadata\n        ", "parameters": {"properties": {"tax_type_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Tax Type Id"}, "items_per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 20, "title": "Items Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "title": "get_tax_return_frequenciesArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863916+00:00"}, {"name": "get_tax_return_frequency_by_id", "description": "Returns a Tax Return Frequency.\n        \n        This tool retrieves detailed information for a specific tax return frequency\n        from Sage Business Cloud Accounting.\n        \n        Use this tool when the user asks about:\n        - Details of a specific tax return frequency\n        - Information about a tax return frequency by ID\n        - When taxes need to be filed for a particular frequency\n        \n        Args:\n            key: The Tax Return Frequency Key (ID)\n            attributes: Optional attributes to expose for the tax return frequency\n            \n        Returns:\n            Dictionary containing detailed tax return frequency information\n        ", "parameters": {"properties": {"key": {"title": "Key", "type": "string"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "required": ["key"], "title": "get_tax_return_frequency_by_idArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863922+00:00"}, {"name": "get_tax_schemes", "description": "Returns all Tax Schemes.", "parameters": {"properties": {"items_per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 20, "title": "Items Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "title": "get_tax_schemesArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863927+00:00"}, {"name": "get_tax_scheme_by_id", "description": "Returns a Tax Scheme.", "parameters": {"properties": {"key": {"title": "Key", "type": "string"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "required": ["key"], "title": "get_tax_scheme_by_idArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863933+00:00"}, {"name": "get_tax_offices", "description": "Returns all Tax Offices.\n        \n        This tool retrieves tax office data from Sage Business Cloud Accounting.\n        Tax offices represent the national tax authority's offices.\n        \n        Use this tool when the user asks about:\n        - List of all tax offices\n        - Tax authority office information\n        - Tax office codes and names\n        \n        Args:\n            items_per_page: Number of items per page (default: 20, max: 200)\n            page: Page number to retrieve (default: 1)\n            attributes: Optional attributes to expose for the tax offices\n            \n        Returns:\n            Dictionary containing the list of tax offices and metadata\n        ", "parameters": {"properties": {"items_per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 20, "title": "Items Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "title": "get_tax_officesArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863939+00:00"}, {"name": "get_tax_office_by_id", "description": "Returns a Tax Office.\n        \n        This tool retrieves detailed information for a specific tax office\n        from Sage Business Cloud Accounting.\n        \n        Use this tool when the user asks about:\n        - Details of a specific tax office\n        - Information about a tax office by ID\n        - Finding a tax office by its code or name\n        \n        Args:\n            key: The Tax Office Key (ID)\n            attributes: Optional attributes to expose for the tax office\n            \n        Returns:\n            Dictionary containing detailed tax office information\n        ", "parameters": {"properties": {"key": {"title": "Key", "type": "string"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "required": ["key"], "title": "get_tax_office_by_idArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863945+00:00"}, {"name": "get_tax_determinations", "description": "Returns all Tax Determinations.\n        \n        Tax Determination Engine provides an API that given information about an artefact,\n        the business generating the artefact, and the customer of the artefact, provides a\n        set of legislatively valid VAT rates describing the percentage that should be applied per item.\n        \n        Use this tool when the user asks about:\n        - Determining appropriate tax rates for a transaction\n        - Legislatively valid VAT rates for goods or services\n        - Tax determinations for a specific contact\n        - Tax rates that should be applied to a transaction\n        \n        Args:\n            usage: Tax rates with the intended usage (sales or purchases)\n            artefact_id: Optional ID of the artefact\n            artefact_line_amount: Optional amount for the artefact line\n            artefact_line_description: Optional description for the artefact line\n            artefact_line_goods_services_type: Optional type of goods/services (GOODS or SERVICES)\n            artefact_line_id: Optional ID for the artefact line\n            artefact_line_tax_included: Optional flag indicating if tax is included in the line\n            contact_id: Optional contact ID to filter by\n            date: Optional date for the tax determination (ISO format: YYYY-MM-DD)\n            include_destination_vat: Optional flag to include destination VAT rates\n            include_line_determination: Optional flag to include line determination details\n            retailer_rates: Optional flag to display recargo retailer rates\n            items_per_page: Number of items per page (default: 20, max: 200)\n            page: Page number to retrieve (default: 1)\n            attributes: Optional attributes to expose for the tax determinations\n            \n        Returns:\n            Dictionary containing the list of tax determinations and metadata\n        ", "parameters": {"properties": {"usage": {"title": "Usage", "type": "string"}, "artefact_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Artefact Id"}, "artefact_line_amount": {"anyOf": [{"type": "number"}, {"type": "null"}], "default": null, "title": "Artefact Line Amount"}, "artefact_line_description": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Artefact Line Description"}, "artefact_line_goods_services_type": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Artefact Line Goods Services Type"}, "artefact_line_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Artefact Line Id"}, "artefact_line_tax_included": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Artefact Line Tax Included"}, "contact_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Contact Id"}, "date": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Date"}, "include_destination_vat": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Include Destination Vat"}, "include_line_determination": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Include Line Determination"}, "retailer_rates": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Retailer Rates"}, "items_per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 20, "title": "Items Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "required": ["usage"], "title": "get_tax_determinationsArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863951+00:00"}, {"name": "get_attachments", "description": "Returns all Attachments.\n        \n        This tool retrieves attachment data from Sage Business Cloud Accounting.\n        \n        Use this tool when the user asks about:\n        - List of all attachments\n        - Finding attachments by context or type\n        - Recently created or updated attachments\n        \n        Args:\n            attachment_context_id: Optional filter by attachment context ID (requires context_type_id)\n            attachment_context_type_id: Optional filter by context type (requires context_id)\n            legacy_attachment_context_type: Optional legacy context type filter\n            legacy_attachment_context_id: Optional legacy context ID filter\n            updated_or_created_since: Optional limit to attachments changed since a given date\n            deleted_since: Optional limit to attachments deleted since a given date\n            items_per_page: Number of items per page (default: 20, max: 200)\n            page: Page number to retrieve (default: 1)\n            attributes: Optional attributes to expose for the attachments\n            \n        Returns:\n            Dictionary containing the list of attachments and metadata\n        ", "parameters": {"properties": {"attachment_context_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attachment Context Id"}, "attachment_context_type_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attachment Context Type Id"}, "legacy_attachment_context_type": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Legacy Attachment Context Type"}, "legacy_attachment_context_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Legacy Attachment Context Id"}, "updated_or_created_since": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Updated Or Created Since"}, "deleted_since": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Deleted Since"}, "items_per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 20, "title": "Items Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "title": "get_attachmentsArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863957+00:00"}, {"name": "get_attachment_by_id", "description": "Returns a specific Attachment.\n        \n        This tool retrieves detailed information for a specific attachment\n        from Sage Business Cloud Accounting.\n        \n        Use this tool when the user asks about:\n        - Details of a specific attachment\n        - Information about an attachment by ID\n        - Metadata for a particular file\n        \n        Args:\n            key: The Attachment Key (ID)\n            attributes: Optional attributes to expose for the attachment\n            \n        Returns:\n            Dictionary containing detailed attachment information\n        ", "parameters": {"properties": {"key": {"title": "Key", "type": "string"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "required": ["key"], "title": "get_attachment_by_idArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863963+00:00"}, {"name": "get_attachment_file", "description": "Returns an Attachment File.\n        \n        This tool retrieves the actual file content of a specific attachment\n        from Sage Business Cloud Accounting.\n        \n        Use this tool when the user asks about:\n        - Downloading an attachment file\n        - Getting the content of a specific file\n        - Accessing an attachment's actual data\n        \n        Args:\n            key: The Attachment Key (ID)\n            \n        Returns:\n            Dictionary containing the file content as base64 and metadata\n        ", "parameters": {"properties": {"key": {"title": "Key", "type": "string"}}, "required": ["key"], "title": "get_attachment_fileArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863969+00:00"}, {"name": "get_attachment_context_types", "description": "Returns all Attachment Context Types.\n        \n        This tool retrieves the available context types for attachments from \n        Sage Business Cloud Accounting. Context types define what business objects\n        attachments can be associated with (e.g. SALES_INVOICE, PURCHASE_INVOICE, CONTACT).\n        \n        Use this tool when the user asks about:\n        - Types of items that can have attachments\n        - Available contexts for file uploads\n        - Attachment categorization options\n        \n        Args:\n            items_per_page: Number of items per page (default: 20, max: 200)\n            page: Page number to retrieve (default: 1)\n            attributes: Optional attributes to expose for the context types\n            \n        Returns:\n            Dictionary containing the list of attachment context types and metadata\n        ", "parameters": {"properties": {"items_per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 20, "title": "Items Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "title": "get_attachment_context_typesArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863975+00:00"}, {"name": "get_attachment_context_type_by_id", "description": "Returns a specific Attachment Context Type.\n        \n        This tool retrieves detailed information for a specific attachment context type\n        from Sage Business Cloud Accounting.\n        \n        Use this tool when the user asks about:\n        - Details of a specific attachment context type\n        - Information about an attachment context type by ID\n        - Properties of a specific attachment category\n        \n        Args:\n            key: The Attachment Context Type Key (ID)\n            attributes: Optional attributes to expose for the context type\n            \n        Returns:\n            Dictionary containing detailed attachment context type information\n        ", "parameters": {"properties": {"key": {"title": "Key", "type": "string"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "required": ["key"], "title": "get_attachment_context_type_by_idArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863980+00:00"}, {"name": "get_corrective_reason_codes", "description": "Returns all Corrective Reason Codes.\n        \n        ### Endpoint Availability\n        * Accounting Plus: 🇪🇸\n        * Accounting Standard: 🇪🇸\n        * Accounting Start: 🇪🇸\n        \n        Args:\n            items_per_page: Returns the given number of Corrective Reason Codes per request (default: 20, max: 200)\n            page: Go to specific page of Corrective Reason Codes (default: 1)\n            attributes: Specify the attributes that you want to expose for the Corrective Reason Codes (expose all attributes with 'all')\n            \n        Returns:\n            Dictionary containing the list of corrective reason codes and metadata\n        ", "parameters": {"properties": {"items_per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 20, "title": "Items Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "title": "get_corrective_reason_codesArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863985+00:00"}, {"name": "get_corrective_reason_code_by_id", "description": "Returns a Corrective Reason Code.\n        \n        ### Endpoint Availability\n        * Accounting Plus: 🇪🇸\n        * Accounting Standard: 🇪🇸\n        * Accounting Start: 🇪🇸\n        \n        Args:\n            key: The Corrective Reason Code Key\n            attributes: Specify the attributes that you want to expose for the Corrective Reason Code (expose all attributes with 'all')\n            \n        Returns:\n            Dictionary containing detailed corrective reason code information\n        ", "parameters": {"properties": {"key": {"title": "Key", "type": "string"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "required": ["key"], "title": "get_corrective_reason_code_by_idArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863992+00:00"}, {"name": "get_quick_entry_types", "description": "Returns all Quick Entry Types.\n        \n        ### Endpoint Availability\n        * Accounting Plus: 🇨🇦, 🇪🇸, 🇫🇷, 🇬🇧, 🇮🇪, 🇺🇸\n        * Accounting Standard: 🇬🇧, 🇮🇪\n        * Accounting Start: 🇨🇦, 🇪🇸, 🇫🇷, 🇬🇧, 🇮🇪, 🇺🇸\n        \n        Args:\n            items_per_page: Returns the given number of Batch Entry Types per request (default: 20, max: 200)\n            page: Go to specific page of Batch Entry Types (default: 1)\n            attributes: Specify the attributes that you want to expose for the Batch Entry Types (expose all attributes with 'all')\n            \n        Returns:\n            Dictionary containing the list of quick entry types and metadata\n        ", "parameters": {"properties": {"items_per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 20, "title": "Items Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "title": "get_quick_entry_typesArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.863997+00:00"}, {"name": "get_quick_entry_type_by_id", "description": "Returns a Quick Entry Type.\n        \n        ### Endpoint Availability\n        * Accounting Plus: 🇨🇦, 🇪🇸, 🇫🇷, 🇬🇧, 🇮🇪, 🇺🇸\n        * Accounting Standard: 🇬🇧, 🇮🇪\n        * Accounting Start: 🇨🇦, 🇪🇸, 🇫🇷, 🇬🇧, 🇮🇪, 🇺🇸\n        \n        Args:\n            key: The Quick Entry Type Key\n            attributes: Specify the attributes that you want to expose for the Batch Entry Type (expose all attributes with 'all')\n            \n        Returns:\n            Dictionary containing detailed quick entry type information\n        ", "parameters": {"properties": {"key": {"title": "Key", "type": "string"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "required": ["key"], "title": "get_quick_entry_type_by_idArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.864003+00:00"}, {"name": "get_artefact_statuses", "description": "Returns all Artefact Statuses.\n        \n        ### Endpoint Availability\n        * Accounting Plus: 🇨🇦, 🇪🇸, 🇫🇷, 🇬🇧, 🇮🇪, 🇺🇸\n        * Accounting Standard: 🇬🇧, 🇮🇪\n        * Accounting Start: 🇨🇦, 🇪🇸, 🇫🇷, 🇬🇧, 🇮🇪, 🇺🇸\n        \n        ### Access Control Restrictions\n        Requires the authenticated user to have any mentioned role in one of the listed areas:\n        * Area: `Sales`: Read Only, Restricted Access, Full Access\n        * Area: `Purchases`: Read Only, Restricted Access, Full Access\n        \n        Args:\n            items_per_page: Returns the given number of Artefact Statuses per request (default: 20, max: 200)\n            page: Go to specific page of Artefact Statuses (default: 1)\n            attributes: Specify the attributes that you want to expose for the Artefact Statuses (expose all attributes with 'all')\n            \n        Returns:\n            Dictionary containing the list of artefact statuses and metadata\n        ", "parameters": {"properties": {"items_per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 20, "title": "Items Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "title": "get_artefact_statusesArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.864009+00:00"}, {"name": "get_artefact_status_by_id", "description": "Returns a Artefact Status.\n        \n        ### Endpoint Availability\n        * Accounting Plus: 🇨🇦, 🇪🇸, 🇫🇷, 🇬🇧, 🇮🇪, 🇺🇸\n        * Accounting Standard: 🇬🇧, 🇮🇪\n        * Accounting Start: 🇨🇦, 🇪🇸, 🇫🇷, 🇬🇧, 🇮🇪, 🇺🇸\n        \n        ### Access Control Restrictions\n        Requires the authenticated user to have any mentioned role in one of the listed areas:\n        * Area: `Sales`: Read Only, Restricted Access, Full Access\n        * Area: `Purchases`: Read Only, Restricted Access, Full Access\n        \n        Args:\n            key: The Artefact Status Key\n            attributes: Specify the attributes that you want to expose for the Artefact Status (expose all attributes with 'all')\n            \n        Returns:\n            Dictionary containing detailed artefact status information\n        ", "parameters": {"properties": {"key": {"title": "Key", "type": "string"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "required": ["key"], "title": "get_artefact_status_by_idArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.864015+00:00"}, {"name": "get_eu_goods_services_types", "description": "Returns all EU Goods Services Types.\n        \n        ### Endpoint Availability\n        * Accounting Plus: 🇨🇦, 🇪🇸, 🇫🇷, 🇬🇧, 🇮🇪, 🇺🇸\n        * Accounting Standard: 🇬🇧, 🇮🇪\n        * Accounting Start: 🇨🇦, 🇪🇸, 🇫🇷, 🇬🇧, 🇮🇪, 🇺🇸\n        \n        Args:\n            items_per_page: Returns the given number of EU Goods Services Types per request (default: 20, max: 200)\n            page: Go to specific page of EU Goods Services Types (default: 1)\n            attributes: Specify the attributes that you want to expose for the EU Goods Services Types (expose all attributes with 'all')\n            \n        Returns:\n            Dictionary containing the list of EU goods services types and metadata\n        ", "parameters": {"properties": {"items_per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 20, "title": "Items Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "title": "get_eu_goods_services_typesArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.864020+00:00"}, {"name": "get_eu_goods_services_type_by_id", "description": "Returns a EU Goods Services Type.\n        \n        ### Endpoint Availability\n        * Accounting Plus: 🇨🇦, 🇪🇸, 🇫🇷, 🇬🇧, 🇮🇪, 🇺🇸\n        * Accounting Standard: 🇬🇧, 🇮🇪\n        * Accounting Start: 🇨🇦, 🇪🇸, 🇫🇷, 🇬🇧, 🇮🇪, 🇺🇸\n        \n        Args:\n            key: The EU Goods Services Type Key\n            attributes: Specify the attributes that you want to expose for the EU Goods Services Type (expose all attributes with 'all')\n            \n        Returns:\n            Dictionary containing detailed EU goods services type information\n        ", "parameters": {"properties": {"key": {"title": "Key", "type": "string"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "required": ["key"], "title": "get_eu_goods_services_type_by_idArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.864026+00:00"}, {"name": "get_currencies", "description": "Returns all Currencies.\n        \n        ### Endpoint Availability\n        * Accounting Plus: 🇨🇦, 🇪🇸, 🇫🇷, 🇬🇧, 🇮🇪, 🇺🇸\n        * Accounting Standard: 🇬🇧, 🇮🇪\n        * Accounting Start: 🇨🇦, 🇪🇸, 🇫🇷, 🇬🇧, 🇮🇪, 🇺🇸\n        \n        Args:\n            items_per_page: Returns the given number of Currencies per request (default: 20, max: 200)\n            page: Go to specific page of Currencies (default: 1)\n            attributes: Specify the attributes that you want to expose for the Currencies (expose all attributes with 'all')\n            \n        Returns:\n            Dictionary containing the list of currencies and metadata\n        ", "parameters": {"properties": {"items_per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 20, "title": "Items Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "title": "get_currenciesArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.864032+00:00"}, {"name": "get_currency_by_id", "description": "Returns a Currency.\n        \n        ### Endpoint Availability\n        * Accounting Plus: 🇨🇦, 🇪🇸, 🇫🇷, 🇬🇧, 🇮🇪, 🇺🇸\n        * Accounting Standard: 🇬🇧, 🇮🇪\n        * Accounting Start: 🇨🇦, 🇪🇸, 🇫🇷, 🇬🇧, 🇮🇪, 🇺🇸\n        \n        Args:\n            key: The Currency Key\n            attributes: Specify the attributes that you want to expose for the Currency (expose all attributes with 'all')\n            \n        Returns:\n            Dictionary containing detailed currency information\n        ", "parameters": {"properties": {"key": {"title": "Key", "type": "string"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "required": ["key"], "title": "get_currency_by_idArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.864038+00:00"}, {"name": "get_business_exchange_rates", "description": "Returns all Business Exchange Rates.\n        \n        ### Endpoint Availability\n        * Accounting Plus: 🇨🇦, 🇪🇸, 🇫🇷, 🇬🇧, 🇮🇪, 🇺🇸\n        * Accounting Standard: 🇬🇧, 🇮🇪\n        * Accounting Start: 🇨🇦, 🇪🇸, 🇫🇷, 🇬🇧, 🇮🇪, 🇺🇸\n        \n        ### Access Control Restrictions\n        Requires the authenticated user to have any of the following roles in the area `Settings`: Full Access, Read Only\n        \n        This endpoint exposes exchange rates defined by the user. Each exchange rate has a rate\n        to convert the business' base currency to a foreign one and vice versa.\n        \n        The 'use_live_exchange_rates' attribute on the Financial Settings endpoint tells you\n        whether the business uses live rates or these user-defined rates.\n        \n        Args:\n            items_per_page: Returns the given number of Business Exchange Rates per request (default: 20, max: 200)\n            page: Go to specific page of Business Exchange Rates (default: 1)\n            attributes: Specify the attributes that you want to expose for the Business Exchange Rates (expose all attributes with 'all')\n            \n        Returns:\n            Dictionary containing the list of business exchange rates and metadata\n        ", "parameters": {"properties": {"items_per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 20, "title": "Items Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "title": "get_business_exchange_ratesArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.864043+00:00"}, {"name": "get_business_exchange_rate_by_id", "description": "Returns a Business Exchange Rate.\n        \n        ### Endpoint Availability\n        * Accounting Plus: 🇨🇦, 🇪🇸, 🇫🇷, 🇬🇧, 🇮🇪, 🇺🇸\n        * Accounting Standard: 🇬🇧, 🇮🇪\n        * Accounting Start: 🇨🇦, 🇪🇸, 🇫🇷, 🇬🇧, 🇮🇪, 🇺🇸\n        \n        ### Access Control Restrictions\n        Requires the authenticated user to have any of the following roles in the area `Settings`: Full Access, Read Only\n        \n        Returns a user-defined exchange rate for a specific currency.\n        \n        Example response includes:\n        - rate: The exchange rate\n        - inverse_rate: The inverse exchange rate\n        - base_currency: The base currency for the business\n        - currency: The foreign currency\n        - created_at: When the rate was created\n        - updated_at: When the rate was last updated\n        \n        Args:\n            key: The Business Exchange Rate Key\n            attributes: Specify the attributes that you want to expose for the Business Exchange Rate (expose all attributes with 'all')\n            \n        Returns:\n            Dictionary containing detailed business exchange rate information\n        ", "parameters": {"properties": {"key": {"title": "Key", "type": "string"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "required": ["key"], "title": "get_business_exchange_rate_by_idArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.864049+00:00"}, {"name": "get_exchange_rates", "description": "Returns all Exchange Rates.\n        \n        ### Endpoint Availability\n        * Accounting Plus: 🇨🇦, 🇪🇸, 🇫🇷, 🇬🇧, 🇮🇪, 🇺🇸\n        * Accounting Standard: 🇬🇧, 🇮🇪\n        * Accounting Start: 🇨🇦, 🇪🇸, 🇫🇷, 🇬🇧, 🇮🇪, 🇺🇸\n        \n        This endpoint combines Business Exchange Rates (user-defined) with Live Exchange Rates\n        (automatically updated). If there is no business-defined rate for a currency, a Live\n        Exchange Rate is provided instead.\n        \n        The 'use_live_exchange_rates' attribute on the Financial Settings endpoint indicates\n        whether the business uses live rates or business-defined ones.\n        \n        Args:\n            items_per_page: Returns the given number of Currencies per request (default: 20, max: 200)\n            page: Go to specific page of Currencies (default: 1)\n            attributes: Specify the attributes that you want to expose for the Currencies (expose all attributes with 'all')\n            \n        Returns:\n            Dictionary containing the list of exchange rates and metadata\n        ", "parameters": {"properties": {"items_per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 20, "title": "Items Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "title": "get_exchange_ratesArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.864054+00:00"}, {"name": "get_exchange_rate_by_id", "description": "Returns an Exchange Rate.\n        \n        ### Endpoint Availability\n        * Accounting Plus: 🇨🇦, 🇪🇸, 🇫🇷, 🇬🇧, 🇮🇪, 🇺🇸\n        * Accounting Standard: 🇬🇧, 🇮🇪\n        * Accounting Start: 🇨🇦, 🇪🇸, 🇫🇷, 🇬🇧, 🇮🇪, 🇺🇸\n        \n        Returns either a business-defined exchange rate or a live exchange rate for the\n        specified currency, depending on availability. If a business-defined rate exists,\n        it will be returned; otherwise, a live exchange rate is provided.\n        \n        Example response includes:\n        - rate: The exchange rate\n        - inverse_rate: The inverse exchange rate\n        - base_currency: The base currency for the business\n        - currency: The foreign currency\n        - created_at: When the rate was created\n        - updated_at: When the rate was last updated\n        \n        Args:\n            key: The Exchange Rate Key\n            attributes: Specify the attributes that you want to expose for the Currency (expose all attributes with 'all')\n            \n        Returns:\n            Dictionary containing detailed exchange rate information\n        ", "parameters": {"properties": {"key": {"title": "Key", "type": "string"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "required": ["key"], "title": "get_exchange_rate_by_idArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.864061+00:00"}, {"name": "get_live_exchange_rates", "description": "Returns all Live Exchange Rates.\n        \n        ### Endpoint Availability\n        * Accounting Plus: 🇨🇦, 🇪🇸, 🇫🇷, 🇬🇧, 🇮🇪, 🇺🇸\n        * Accounting Standard: 🇬🇧, 🇮🇪\n        * Accounting Start: 🇨🇦, 🇪🇸, 🇫🇷, 🇬🇧, 🇮🇪, 🇺🇸\n        \n        Returns automatically updated exchange rates available for a business. \n        Each exchange rate has a rate to convert the business' base currency to a \n        foreign one and vice versa.\n        \n        The 'use_live_exchange_rates' attribute on the Financial Settings endpoint \n        tells you whether the business uses these rates or user-defined ones.\n        \n        Args:\n            items_per_page: Returns the given number of Currencies per request (default: 20, max: 200)\n            page: Go to specific page of Currencies (default: 1)\n            attributes: Specify the attributes that you want to expose for the Currencies (expose all attributes with 'all')\n            \n        Returns:\n            Dictionary containing the list of live exchange rates and metadata\n        ", "parameters": {"properties": {"items_per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 20, "title": "Items Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "title": "get_live_exchange_ratesArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.864066+00:00"}, {"name": "get_live_exchange_rate_by_id", "description": "Returns a Live Exchange Rate.\n        \n        ### Endpoint Availability\n        * Accounting Plus: 🇨🇦, 🇪🇸, 🇫🇷, 🇬🇧, 🇮🇪, 🇺🇸\n        * Accounting Standard: 🇬🇧, 🇮🇪\n        * Accounting Start: 🇨🇦, 🇪🇸, 🇫🇷, 🇬🇧, 🇮🇪, 🇺🇸\n        \n        Returns an automatically updated exchange rate for a specific currency.\n        \n        Example response includes:\n        - rate: The exchange rate\n        - inverse_rate: The inverse exchange rate\n        - base_currency: The base currency for the business\n        - currency: The foreign currency\n        - retrieved_at: When the rate was retrieved\n        - updated_at: When the rate was last updated\n        \n        Args:\n            key: The Live Exchange Rate Key\n            attributes: Specify the attributes that you want to expose for the Currency (expose all attributes with 'all')\n            \n        Returns:\n            Dictionary containing detailed live exchange rate information\n        ", "parameters": {"properties": {"key": {"title": "Key", "type": "string"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "required": ["key"], "title": "get_live_exchange_rate_by_idArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.864071+00:00"}, {"name": "get_contact_opening_balances", "description": "Returns all Contact Opening Balances with optional filtering.\n        \n        Invoices or credit notes created in a previous system and outstanding\n        at the point of transferring to Sage Business Cloud Accounting are\n        treated as Contact Opening Balances.\n        \n        Use this tool when you need to:\n        - List all contact opening balances\n        - Filter opening balances by contact\n        - Search for specific opening balances\n        \n        Args:\n            search: Filter by reference or contact name\n            contact_type_id: Filter by contact type ID\n            contact_id: Filter by contact ID\n            updated_or_created_since: Limit to entries changed since given date\n            deleted_since: Limit to entries deleted since given date \n            items_per_page: Number of items per page (default: 20, max: 200)\n            page: Page number to retrieve (default: 1)\n            attributes: Specify attributes to expose (comma-separated or 'all')\n            \n        Returns:\n            Dictionary containing list of contact opening balances and metadata\n        ", "parameters": {"properties": {"search": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Search"}, "contact_type_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Contact Type Id"}, "contact_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Contact Id"}, "updated_or_created_since": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Updated Or Created Since"}, "deleted_since": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Deleted Since"}, "items_per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 20, "title": "Items Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "title": "get_contact_opening_balancesArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.864077+00:00"}, {"name": "get_contact_opening_balance_by_id", "description": "Returns a specific Contact Opening Balance by ID.\n        \n        Use this tool when you need to:\n        - Get detailed information about a specific contact opening balance\n        - Examine the details of a particular outstanding invoice/credit note\n        \n        Args:\n            key: The unique identifier of the contact opening balance\n            attributes: Specify attributes to expose (comma-separated or 'all')\n            \n        Returns:\n            Dictionary containing detailed contact opening balance information\n        ", "parameters": {"properties": {"key": {"title": "Key", "type": "string"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "required": ["key"], "title": "get_contact_opening_balance_by_idArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.864082+00:00"}, {"name": "create_contact_opening_balance", "description": "Creates a new Contact Opening Balance.\n        \n        Use this tool when you need to:\n        - Create a new opening balance for a contact\n        - Add a pre-existing invoice or credit note to the system\n        \n        Args:\n            contact_opening_balance_type_id: Type of opening balance (INVOICE or CREDIT_NOTE)\n            date: The date of the opening balance (YYYY-MM-DD)\n            contact_id: The contact the opening balance relates to\n            reference: The reference for the opening balance\n            total_amount: The total amount of the opening balance\n            transaction_type_id: The transaction type ID (optional)\n            details: A description of the opening balance (optional)\n            net_amount: The net amount before tax (optional)\n            tax_rate_id: The tax rate ID (optional)\n            tax_amount: The tax amount (optional)\n            currency_id: The currency ID (optional)\n            exchange_rate: The exchange rate (optional)\n            base_currency_net_amount: Net amount in base currency (optional)\n            base_currency_tax_amount: Tax amount in base currency (optional)\n            base_currency_total_amount: Total amount in base currency (optional)\n            \n        Returns:\n            The created contact opening balance\n        ", "parameters": {"properties": {"contact_opening_balance_type_id": {"title": "Contact Opening Balance Type Id", "type": "string"}, "date": {"title": "Date", "type": "string"}, "contact_id": {"title": "Contact Id", "type": "string"}, "reference": {"title": "Reference", "type": "string"}, "total_amount": {"title": "Total Amount", "type": "number"}, "transaction_type_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Transaction Type Id"}, "details": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Details"}, "net_amount": {"anyOf": [{"type": "number"}, {"type": "null"}], "default": null, "title": "Net Amount"}, "tax_rate_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Tax Rate Id"}, "tax_amount": {"anyOf": [{"type": "number"}, {"type": "null"}], "default": null, "title": "Tax Amount"}, "currency_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "<PERSON><PERSON><PERSON><PERSON> Id"}, "exchange_rate": {"anyOf": [{"type": "number"}, {"type": "null"}], "default": null, "title": "Exchange Rate"}, "base_currency_net_amount": {"anyOf": [{"type": "number"}, {"type": "null"}], "default": null, "title": "Base Currency Net Amount"}, "base_currency_tax_amount": {"anyOf": [{"type": "number"}, {"type": "null"}], "default": null, "title": "Base Currency Tax Amount"}, "base_currency_total_amount": {"anyOf": [{"type": "number"}, {"type": "null"}], "default": null, "title": "Base Currency Total Amount"}}, "required": ["contact_opening_balance_type_id", "date", "contact_id", "reference", "total_amount"], "title": "create_contact_opening_balanceArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.864090+00:00"}, {"name": "update_contact_opening_balance", "description": "Updates an existing Contact Opening Balance.\n        \n        Use this tool when you need to:\n        - Update details of an existing opening balance\n        - Change amounts, taxes, or other properties\n        \n        Args:\n            key: The unique identifier of the contact opening balance\n            contact_opening_balance_type_id: Type of opening balance\n            date: The date of the opening balance\n            contact_id: The contact the opening balance relates to\n            reference: The reference for the opening balance\n            total_amount: The total amount of the opening balance\n            transaction_type_id: The transaction type ID\n            details: A description of the opening balance\n            net_amount: The net amount before tax\n            tax_rate_id: The tax rate ID\n            tax_amount: The tax amount\n            currency_id: The currency ID\n            exchange_rate: The exchange rate\n            base_currency_net_amount: Net amount in base currency\n            base_currency_tax_amount: Tax amount in base currency\n            base_currency_total_amount: Total amount in base currency\n            \n        Returns:\n            The updated contact opening balance\n        ", "parameters": {"properties": {"key": {"title": "Key", "type": "string"}, "contact_opening_balance_type_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Contact Opening Balance Type Id"}, "date": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Date"}, "contact_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Contact Id"}, "reference": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Reference"}, "total_amount": {"anyOf": [{"type": "number"}, {"type": "null"}], "default": null, "title": "Total Amount"}, "transaction_type_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Transaction Type Id"}, "details": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Details"}, "net_amount": {"anyOf": [{"type": "number"}, {"type": "null"}], "default": null, "title": "Net Amount"}, "tax_rate_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Tax Rate Id"}, "tax_amount": {"anyOf": [{"type": "number"}, {"type": "null"}], "default": null, "title": "Tax Amount"}, "currency_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "<PERSON><PERSON><PERSON><PERSON> Id"}, "exchange_rate": {"anyOf": [{"type": "number"}, {"type": "null"}], "default": null, "title": "Exchange Rate"}, "base_currency_net_amount": {"anyOf": [{"type": "number"}, {"type": "null"}], "default": null, "title": "Base Currency Net Amount"}, "base_currency_tax_amount": {"anyOf": [{"type": "number"}, {"type": "null"}], "default": null, "title": "Base Currency Tax Amount"}, "base_currency_total_amount": {"anyOf": [{"type": "number"}, {"type": "null"}], "default": null, "title": "Base Currency Total Amount"}}, "required": ["key"], "title": "update_contact_opening_balanceArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.864096+00:00"}, {"name": "delete_contact_opening_balance", "description": "Deletes a Contact Opening Balance.\n        \n        Use this tool when you need to:\n        - Remove an incorrectly entered opening balance\n        - Clean up test or duplicate data\n        \n        Args:\n            key: The unique identifier of the contact opening balance\n            \n        Returns:\n            Success confirmation or error details\n        ", "parameters": {"properties": {"key": {"title": "Key", "type": "string"}}, "required": ["key"], "title": "delete_contact_opening_balanceArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.864101+00:00"}, {"name": "get_contact_opening_balance_types", "description": "Returns all Contact Opening Balance Types.\n        \n        This provides type IDs that can be used when creating contact opening balances.\n        Currently there are two possible types: INVOICE and CREDIT_NOTE.\n        \n        Use this tool when you need to:\n        - Determine valid types for contact opening balances\n        - Get type IDs for creating contact opening balances\n        \n        Args:\n            items_per_page: Number of items per page (default: 20, max: 200)\n            page: Page number to retrieve (default: 1)\n            attributes: Specify attributes to expose (comma-separated or 'all')\n            \n        Returns:\n            Dictionary containing list of contact opening balance types\n        ", "parameters": {"properties": {"items_per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 20, "title": "Items Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "title": "get_contact_opening_balance_typesArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.864107+00:00"}, {"name": "get_contact_opening_balance_type_by_id", "description": "Returns a specific Contact Opening Balance Type by ID.\n        \n        Use this tool when you need to:\n        - Get details about a specific contact opening balance type\n        - Verify a type ID before using it\n        \n        Args:\n            key: The unique identifier of the contact opening balance type\n            attributes: Specify attributes to expose (comma-separated or 'all')\n            \n        Returns:\n            Dictionary containing detailed contact opening balance type information\n        ", "parameters": {"properties": {"key": {"title": "Key", "type": "string"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "required": ["key"], "title": "get_contact_opening_balance_type_by_idArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.864112+00:00"}, {"name": "get_bank_opening_balances", "description": "Returns all Bank Opening Balances.\n        \n        Bank opening balances represent balances brought forward from a previous system\n        at the point of transferring to Sage Business Cloud Accounting.\n        \n        Use this tool when you need to:\n        - List all bank opening balances\n        - Filter opening balances by bank account\n        - View historical bank opening balances\n        \n        Args:\n            updated_or_created_since: Limit to balances changed since given date\n            bank_account_id: Filter by bank account ID\n            items_per_page: Number of items per page (default: 20, max: 200)\n            page: Page number to retrieve (default: 1)\n            attributes: Specify attributes to expose (comma-separated or 'all')\n            \n        Returns:\n            Dictionary containing list of bank opening balances and metadata\n        ", "parameters": {"properties": {"updated_or_created_since": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Updated Or Created Since"}, "bank_account_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Bank Account Id"}, "items_per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 20, "title": "Items Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "title": "get_bank_opening_balancesArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.864118+00:00"}, {"name": "get_bank_opening_balance_by_id", "description": "Returns a specific Bank Opening Balance by ID.\n        \n        Use this tool when you need to:\n        - Get detailed information about a specific bank opening balance\n        - Examine the details of a particular bank account balance\n        \n        Args:\n            key: The unique identifier of the bank opening balance\n            attributes: Specify attributes to expose (comma-separated or 'all')\n            \n        Returns:\n            Dictionary containing detailed bank opening balance information\n        ", "parameters": {"properties": {"key": {"title": "Key", "type": "string"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "required": ["key"], "title": "get_bank_opening_balance_by_idArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.864123+00:00"}, {"name": "create_bank_opening_balance", "description": "Creates a new Bank Opening Balance.\n        \n        Use this tool when you need to:\n        - Create a new opening balance for a bank account\n        - Set up initial account balances in Sage Business Cloud Accounting\n        \n        Args:\n            bank_account_id: The bank account the opening balance relates to\n            date: The date of the opening balance (YYYY-MM-DD)\n            debit: The debit amount of the opening balance\n            credit: The credit amount of the opening balance\n            transaction_type_id: The transaction type ID (optional)\n            \n        Returns:\n            The created bank opening balance\n        ", "parameters": {"properties": {"bank_account_id": {"title": "Bank Account Id", "type": "string"}, "date": {"title": "Date", "type": "string"}, "debit": {"title": "Debit", "type": "number"}, "credit": {"title": "Credit", "type": "number"}, "transaction_type_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Transaction Type Id"}}, "required": ["bank_account_id", "date", "debit", "credit"], "title": "create_bank_opening_balanceArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.864129+00:00"}, {"name": "update_bank_opening_balance", "description": "Updates an existing Bank Opening Balance.\n        \n        Use this tool when you need to:\n        - Update details of an existing bank opening balance\n        - Modify amounts or dates\n        \n        Args:\n            key: The unique identifier of the bank opening balance\n            bank_account_id: The bank account the opening balance relates to\n            date: The date of the opening balance\n            debit: The debit amount of the opening balance\n            credit: The credit amount of the opening balance\n            transaction_type_id: The transaction type ID\n            \n        Returns:\n            The updated bank opening balance\n        ", "parameters": {"properties": {"key": {"title": "Key", "type": "string"}, "bank_account_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Bank Account Id"}, "date": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Date"}, "debit": {"anyOf": [{"type": "number"}, {"type": "null"}], "default": null, "title": "Debit"}, "credit": {"anyOf": [{"type": "number"}, {"type": "null"}], "default": null, "title": "Credit"}, "transaction_type_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Transaction Type Id"}}, "required": ["key"], "title": "update_bank_opening_balanceArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.864134+00:00"}, {"name": "delete_bank_opening_balance", "description": "Deletes a Bank Opening Balance.\n        \n        Use this tool when you need to:\n        - Remove an incorrectly entered bank opening balance\n        - Clean up test or duplicate data\n        \n        Args:\n            key: The unique identifier of the bank opening balance\n            \n        Returns:\n            Success confirmation or error details\n        ", "parameters": {"properties": {"key": {"title": "Key", "type": "string"}}, "required": ["key"], "title": "delete_bank_opening_balanceArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.864140+00:00"}, {"name": "get_ledger_account_opening_balances", "description": "Returns all Ledger Account Opening Balances.\n        \n        Ledger account opening balances represent balances brought forward from a previous\n        system at the point of transferring to Sage Business Cloud Accounting.\n        \n        Use this tool when you need to:\n        - List all ledger account opening balances\n        - View historical ledger account balances\n        - Check initial values for accounts\n        \n        Args:\n            updated_or_created_since: Limit to balances changed since given date\n            items_per_page: Number of items per page (default: 20, max: 200)\n            page: Page number to retrieve (default: 1)\n            attributes: Specify attributes to expose (comma-separated or 'all')\n            \n        Returns:\n            Dictionary containing list of ledger account opening balances and metadata\n        ", "parameters": {"properties": {"updated_or_created_since": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Updated Or Created Since"}, "items_per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 20, "title": "Items Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "title": "get_ledger_account_opening_balancesArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.864145+00:00"}, {"name": "get_ledger_account_opening_balance_by_id", "description": "Returns a specific Ledger Account Opening Balance by ID.\n        \n        Use this tool when you need to:\n        - Get detailed information about a specific ledger account opening balance\n        - Examine the details of a particular account balance\n        \n        Args:\n            key: The unique identifier of the ledger account opening balance\n            attributes: Specify attributes to expose (comma-separated or 'all')\n            \n        Returns:\n            Dictionary containing detailed ledger account opening balance information\n        ", "parameters": {"properties": {"key": {"title": "Key", "type": "string"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "required": ["key"], "title": "get_ledger_account_opening_balance_by_idArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.864151+00:00"}, {"name": "create_ledger_account_opening_balance", "description": "Creates a new Ledger Account Opening Balance.\n        \n        Use this tool when you need to:\n        - Create a new opening balance for a ledger account\n        - Set up initial account balances in Sage Business Cloud Accounting\n        \n        Args:\n            ledger_account_id: The ledger account the opening balance relates to\n            debit: The debit amount of the opening balance\n            credit: The credit amount of the opening balance\n            details: A description of the opening balance (optional)\n            \n        Returns:\n            The created ledger account opening balance\n        ", "parameters": {"properties": {"ledger_account_id": {"title": "Ledger Account Id", "type": "string"}, "debit": {"title": "Debit", "type": "number"}, "credit": {"title": "Credit", "type": "number"}, "details": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Details"}}, "required": ["ledger_account_id", "debit", "credit"], "title": "create_ledger_account_opening_balanceArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.864157+00:00"}, {"name": "update_ledger_account_opening_balance", "description": "Updates an existing Ledger Account Opening Balance.\n        \n        Use this tool when you need to:\n        - Update details of an existing ledger account opening balance\n        - Modify amounts or descriptions\n        \n        Args:\n            key: The unique identifier of the ledger account opening balance\n            ledger_account_id: The ledger account the opening balance relates to\n            debit: The debit amount of the opening balance\n            credit: The credit amount of the opening balance\n            details: A description of the opening balance\n            \n        Returns:\n            The updated ledger account opening balance\n        ", "parameters": {"properties": {"key": {"title": "Key", "type": "string"}, "ledger_account_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Ledger Account Id"}, "debit": {"anyOf": [{"type": "number"}, {"type": "null"}], "default": null, "title": "Debit"}, "credit": {"anyOf": [{"type": "number"}, {"type": "null"}], "default": null, "title": "Credit"}, "details": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Details"}}, "required": ["key"], "title": "update_ledger_account_opening_balanceArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.864162+00:00"}, {"name": "delete_ledger_account_opening_balance", "description": "Deletes a Ledger Account Opening Balance.\n        \n        Use this tool when you need to:\n        - Remove an incorrectly entered ledger account opening balance\n        - Clean up test or duplicate data\n        \n        Args:\n            key: The unique identifier of the ledger account opening balance\n            \n        Returns:\n            Success confirmation or error details\n        ", "parameters": {"properties": {"key": {"title": "Key", "type": "string"}}, "required": ["key"], "title": "delete_ledger_account_opening_balanceArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.864168+00:00"}, {"name": "get_opening_balance_journals", "description": "Returns all Opening Balance Journals.\n        \n        Opening balance journals represent ledger account balances brought forward\n        from a previous system at the point of transferring to Sage Business Cloud Accounting.\n        These values are normally brought forward from a Trial Balance produced from \n        the previous system.\n        \n        Use this tool when you need to:\n        - List all opening balance journals\n        - View journal entries for beginning balances\n        - Review imported trial balance data\n        \n        Args:\n            items_per_page: Number of items per page (default: 20, max: 200)\n            page: Page number to retrieve (default: 1)\n            attributes: Specify attributes to expose (comma-separated or 'all')\n            \n        Returns:\n            Dictionary containing list of opening balance journals and metadata\n        ", "parameters": {"properties": {"items_per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 20, "title": "Items Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "title": "get_opening_balance_journalsArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.864174+00:00"}, {"name": "get_opening_balance_journal_by_id", "description": "Returns a specific Opening Balance Journal by ID.\n        \n        Use this tool when you need to:\n        - Get detailed information about a specific opening balance journal\n        - Examine the details of a particular journal including line items\n        - View imported trial balance details\n        \n        Args:\n            key: The unique identifier of the opening balance journal\n            attributes: Specify attributes to expose (comma-separated or 'all')\n            \n        Returns:\n            Dictionary containing detailed opening balance journal information\n        ", "parameters": {"properties": {"key": {"title": "Key", "type": "string"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "required": ["key"], "title": "get_opening_balance_journal_by_idArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.864180+00:00"}, {"name": "create_opening_balance_journal", "description": "Creates a new Opening Balance Journal.\n        \n        Use this tool when you need to:\n        - Create a new opening balance journal\n        - Import a trial balance from a previous system\n        - Set up initial balances for multiple ledger accounts\n        \n        Args:\n            journal_lines: List of journal line objects. Each line object must have these properties:\n                {\n                    \"ledger_account_id\": \"string\", (required)\n                    \"debit\": number (optional),\n                    \"credit\": number (optional),\n                    \"details\": \"string\" (optional)\n                }\n            date: The date of the opening balance journal (YYYY-MM-DD)\n            reference: A reference for the opening balance journal\n            \n        Returns:\n            The created opening balance journal\n        ", "parameters": {"properties": {"journal_lines": {"default": null, "items": {}, "title": "Journal Lines", "type": "array"}, "date": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Date"}, "reference": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Reference"}}, "title": "create_opening_balance_journalArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.864185+00:00"}, {"name": "delete_opening_balance_journal", "description": "Deletes an Opening Balance Journal.\n        \n        Use this tool when you need to:\n        - Remove an incorrectly entered opening balance journal\n        - Delete a trial balance import that contained errors\n        - Clean up test or duplicate data\n        \n        Args:\n            key: The unique identifier of the opening balance journal\n            \n        Returns:\n            Success confirmation or error details\n        ", "parameters": {"properties": {"key": {"title": "Key", "type": "string"}}, "required": ["key"], "title": "delete_opening_balance_journalArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.864190+00:00"}, {"name": "get_business_settings", "description": "Returns all Business Settings.\n        \n        This tool retrieves the business settings from Sage Business Cloud Accounting.\n        \n        Business settings include:\n        - Business type (e.g., Limited Company, Sole Trader, Partnership)\n        - Country of registration (for UK/Ireland businesses)\n        - Default ledger accounts for various transaction types\n        - Regional settings like SIRET number (France only)\n        - Business activity type (France only)\n        - Legal form type (France only)\n        - Auxiliary accounts visibility (France & Spain only)\n        \n        Use this tool when you need to:\n        - View basic business configuration\n        - Check the default ledger accounts\n        - View business type information\n        - Access region-specific business details\n        \n        Args:\n            ctx: MCP context (automatically provided)\n            \n        Returns:\n            Dictionary containing business settings and configuration\n        ", "parameters": {"properties": {}, "title": "get_business_settingsArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.864195+00:00"}, {"name": "update_business_settings", "description": "Updates Business Settings.\n        \n        This tool allows updating various business settings in Sage Business Cloud Accounting.\n        \n        Use this tool when you need to:\n        - Change the business type\n        - Update country of registration\n        - Modify default ledger accounts\n        - Update region-specific business details (SIRET, RCS number, etc.)\n        - Set the quick start wizard completion status\n        \n        Args:\n            business_type_id: ID of the business type (e.g., \"LIMITED\", \"SOL<PERSON>\", \"PARTNER\")\n            country_of_registration_id: ID of the country of registration (UK/Ireland businesses only)\n            siret: SIRET Number (France only)\n            management_centre_member: Member of Approved Management Centres flag (France only)\n            rcs_number: RCS Number (France only)\n            share_capital: Share Capital amount (France only)\n            business_activity_type_id: ID of the Business Activity Type (France only)\n            legal_form_type_id: ID of the Legal Form Type (France only)\n            auxiliary_accounts_visible: Auxiliary Accounts Visible flag (France & Spain only)\n            wizard_complete: Quick Start Wizard completion flag\n            default_ledger_accounts: Dictionary of default ledger account IDs\n                Can include keys like:\n                - bank_charges_ledger_account_id\n                - exchange_rate_gains_ledger_account_id\n                - sales_ledger_account_id\n                - purchase_ledger_account_id\n                - product_sales_ledger_account_id\n                - service_sales_ledger_account_id\n                - etc.\n            \n        Returns:\n            Dictionary containing the updated business settings\n        ", "parameters": {"properties": {"business_type_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Business Type Id"}, "country_of_registration_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Country Of Registration Id"}, "siret": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "<PERSON><PERSON>"}, "management_centre_member": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Management Centre Member"}, "rcs_number": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Rcs Number"}, "share_capital": {"anyOf": [{"type": "number"}, {"type": "null"}], "default": null, "title": "Share Capital"}, "business_activity_type_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Business Activity Type Id"}, "legal_form_type_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Legal Form Type Id"}, "auxiliary_accounts_visible": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Auxiliary Accounts Visible"}, "wizard_complete": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Wizard Complete"}, "default_ledger_accounts": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "default": null, "title": "De<PERSON>ult Ledger Accounts"}}, "title": "update_business_settingsArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.864201+00:00"}, {"name": "get_business_types", "description": "Returns all Business Types.\n        \n        This tool retrieves available business types from Sage Business Cloud Accounting.\n        Business types represent the legal structure of the business, such as \n        Sole Trader, Partnership, or Limited Company.\n        \n        Use this tool when you need to:\n        - Find available business types\n        - Get the proper business type ID for updating business settings\n        - Display business type options to the user\n        \n        Args:\n            items_per_page: Number of items per page (default: 20, max: 200)\n            page: Page number to retrieve (default: 1)\n            attributes: Specify attributes to expose (comma-separated or 'all')\n            \n        Returns:\n            Dictionary containing list of business types and metadata\n        ", "parameters": {"properties": {"items_per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 20, "title": "Items Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "title": "get_business_typesArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.864206+00:00"}, {"name": "get_business_type_by_id", "description": "Returns a specific Business Type.\n        \n        This tool retrieves detailed information about a specific business type \n        from Sage Business Cloud Accounting.\n        \n        Use this tool when you need to:\n        - Get detailed information about a specific business type\n        - Verify a business type before updating business settings\n        \n        Args:\n            key: The business type ID (e.g., \"LIMITED\", \"SOLE\", \"PARTNER\")\n            attributes: Specify attributes to expose (comma-separated or 'all')\n            \n        Returns:\n            Dictionary containing detailed business type information\n        ", "parameters": {"properties": {"key": {"title": "Key", "type": "string"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "required": ["key"], "title": "get_business_type_by_idArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.864211+00:00"}, {"name": "get_countries_of_registration", "description": "Returns all Country of Registrations.\n        \n        This tool retrieves available countries of registration from \n        Sage Business Cloud Accounting. These are relevant for limited \n        businesses in the UK and Ireland.\n        \n        Use this tool when you need to:\n        - Find available countries of registration\n        - Get the proper country of registration ID for updating business settings\n        - Display country options to users with limited companies\n        \n        Args:\n            items_per_page: Number of items per page (default: 20, max: 200)\n            page: Page number to retrieve (default: 1)\n            attributes: Specify attributes to expose (comma-separated or 'all')\n            \n        Returns:\n            Dictionary containing list of countries of registration and metadata\n        ", "parameters": {"properties": {"items_per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 20, "title": "Items Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "title": "get_countries_of_registrationArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.864217+00:00"}, {"name": "get_country_of_registration_by_id", "description": "Returns a specific Country of Registration.\n        \n        This tool retrieves detailed information about a specific country of registration\n        from Sage Business Cloud Accounting.\n        \n        Use this tool when you need to:\n        - Get detailed information about a specific country of registration\n        - Verify a country of registration before updating business settings\n        \n        Args:\n            key: The country of registration ID (e.g., \"ENGLAND_AND_WALES\", \"SCOTLAND\")\n            attributes: Specify attributes to expose (comma-separated or 'all')\n            \n        Returns:\n            Dictionary containing detailed country of registration information\n        ", "parameters": {"properties": {"key": {"title": "Key", "type": "string"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "required": ["key"], "title": "get_country_of_registration_by_idArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.864222+00:00"}, {"name": "get_financial_settings", "description": "Returns all Financial Settings.\n        \n        This tool retrieves the financial settings from Sage Business Cloud Accounting.\n        \n        Financial settings include:\n        - Year end date\n        - Accounting type (accrual/cash)\n        - Accounts start date\n        - Base currency\n        - Multi-currency settings\n        - Tax scheme and tax return frequency\n        - Tax numbers and tax office details\n        - Region-specific tax settings\n        \n        Use this tool when you need to:\n        - View financial year configuration\n        - Check currency settings\n        - View tax configuration\n        - Access MTD (Making Tax Digital) status for UK businesses\n        \n        Args:\n            ctx: MCP context (automatically provided)\n            \n        Returns:\n            Dictionary containing financial settings and configuration\n        ", "parameters": {"properties": {}, "title": "get_financial_settingsArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.864227+00:00"}, {"name": "update_financial_settings", "description": "Updates Financial Settings.\n        \n        This tool allows updating various financial settings in Sage Business Cloud Accounting.\n        \n        Use this tool when you need to:\n        - Change the financial year end date\n        - Update accounting type (accrual/cash)\n        - Modify currency settings\n        - Update tax configuration\n        - Change tax numbers or tax office details\n        \n        Args:\n            year_end_date: The financial year end date (YYYY-MM-DD format)\n            year_end_lockdown_date: The year end lockdown date (YYYY-MM-DD format)\n            accounting_type: The accounting type (\"accrual\" or \"cash\")\n            accounts_start_date: The accounts start date (YYYY-MM-DD format)\n            base_currency_id: The ID of the base currency (e.g., \"GBP\", \"USD\", \"EUR\")\n            multi_currency_enabled: Whether multi-currency is enabled\n            use_live_exchange_rates: Whether to use live exchange rates\n            tax_scheme_id: The ID of the tax scheme\n            tax_return_frequency_id: The ID of the tax return frequency\n            tax_number: The tax number (e.g., VAT number)\n            general_tax_number: The number for various tax report submissions\n            tax_office_id: The ID of the tax office\n            default_irpf_rate: The default IRPF rate (Spain only)\n            flat_rate_tax_percentage: The flat rate tax percentage\n            recoverable_percentage: The partial recoverable tax rate (Canada only)\n            sales_tax_calculation: The method of tax calculation for sales (\"invoice\" or \"cash\")\n            purchase_tax_calculation: The method of tax calculation for purchases (\"invoice\" or \"cash\")\n            postponed_accounting: Whether to use postponed accounting\n            destination_vat: Whether to use destination VAT\n            \n        Returns:\n            Dictionary containing the updated financial settings\n        ", "parameters": {"properties": {"year_end_date": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Year End Date"}, "year_end_lockdown_date": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Year End Lockdown Date"}, "accounting_type": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Accounting Type"}, "accounts_start_date": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Accounts Start Date"}, "base_currency_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Base Currency Id"}, "multi_currency_enabled": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Multi Currency Enabled"}, "use_live_exchange_rates": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Use Live Exchange Rates"}, "tax_scheme_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Tax Scheme Id"}, "tax_return_frequency_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Tax Return Frequency Id"}, "tax_number": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Tax Number"}, "general_tax_number": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "General Tax Number"}, "tax_office_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Tax Office Id"}, "default_irpf_rate": {"anyOf": [{"type": "number"}, {"type": "null"}], "default": null, "title": "Default Irpf Rate"}, "flat_rate_tax_percentage": {"anyOf": [{"type": "number"}, {"type": "null"}], "default": null, "title": "Flat Rate Tax Percentage"}, "recoverable_percentage": {"anyOf": [{"type": "number"}, {"type": "null"}], "default": null, "title": "Recoverable Percentage"}, "sales_tax_calculation": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Sales Tax Calculation"}, "purchase_tax_calculation": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Purchase Tax Calculation"}, "postponed_accounting": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Postponed Accounting"}, "destination_vat": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Destination Vat"}}, "title": "update_financial_settingsArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.864233+00:00"}, {"name": "get_invoice_settings", "description": "Returns all Invoice Settings.\n        \n        This tool retrieves the invoice settings from Sage Business Cloud Accounting.\n        \n        Invoice settings include:\n        - Next invoice/credit note/quote numbers\n        - Number prefixes for invoices, credit notes, quotes, etc.\n        - Default terms and conditions for various document types\n        - Default notes for various document types\n        - Document headings customization\n        - Line item title customization\n        - Footer details\n        - Print settings\n        - Credit terms settings\n        \n        Use this tool when you need to:\n        - View invoice numbering configuration\n        - Check default terms and conditions\n        - View document customization settings\n        - Access credit terms settings\n        \n        Args:\n            ctx: MCP context (automatically provided)\n            \n        Returns:\n            Dictionary containing invoice settings and configuration\n        ", "parameters": {"properties": {}, "title": "get_invoice_settingsArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.864238+00:00"}, {"name": "update_invoice_settings", "description": "Updates Invoice Settings.\n        \n        This tool allows updating various invoice settings in Sage Business Cloud Accounting.\n        \n        Use this tool when you need to:\n        - Change invoice numbering or prefixes\n        - Update default terms and conditions\n        - Modify document customization settings\n        - Change credit terms settings\n        \n        Args:\n            next_invoice_number: The next invoice number to use\n            next_credit_note_number: The next credit note number to use\n            separate_invoice_credit_note_numbering: Whether to use separate numbering for invoices and credit notes\n            sales_invoice_number_prefix: The prefix for sales invoice numbers (max 6 chars)\n            sales_credit_note_number_prefix: The prefix for sales credit note numbers (max 6 chars)\n            invoice_terms_and_conditions: Default terms and conditions for invoices (max 2000 chars)\n            default_note_on_invoice: Default note for invoices (max 2000 chars)\n            default_note_on_credit_note: Default note for credit notes (max 2000 chars)\n            default_note_on_estimate: Default note for estimates (max 2000 chars)\n            default_note_on_quote: Default note for quotes (max 2000 chars)\n            next_quote_number: The next quote number to use\n            quote_number_prefix: The prefix for quote numbers (max 6 chars)\n            estimate_number_prefix: The prefix for estimate numbers (max 6 chars)\n            quote_default_days_to_expiry: Default days until quotes expire\n            estimate_default_days_to_expiry: Default days until estimates expire\n            quote_terms_and_conditions: Default terms and conditions for quotes (max 2000 chars)\n            estimate_terms_and_conditions: Default terms and conditions for estimates (max 2000 chars)\n            delivery_note_terms_and_conditions: Default terms and conditions for delivery notes (max 2000 chars)\n            delivery_note_show_signature: Whether to show signature on delivery notes\n            delivery_note_show_picked: Whether to show picked on delivery notes\n            delivery_note_show_notes: Whether to show notes on delivery notes\n            delivery_note_show_contact_details: Whether to show contact details on delivery notes\n            quick_entry_prefix: The prefix for quick entries (max 6 chars)\n            late_payment_percentage: Late payment percentage (France only)\n            prompt_payment_percentage: Prompt payment percentage (France only)\n            show_auto_entrepreneur: Whether to show auto entrepreneur details (France only)\n            show_insurance: Whether to show insurance details (France only)\n            insurance_area: Insurance area (France only)\n            insurance_type: Insurance type (France only)\n            insurance_text: Insurance text (France only)\n            payment_bank_account_id: The ID of the default payment bank account\n            sales_corrective_invoice_number_prefix: Prefix for sales corrective invoice numbers (Spain only)\n            next_sales_corrective_invoice_number: The next sales corrective invoice number (Spain only)\n            document_headings: Dictionary of document headings customization\n                Can include keys like:\n                - sales_invoice\n                - sales_credit_note\n                - sales_quote\n                - sales_estimate\n                - pro_forma\n                - remittance_advice\n                - statement\n                - delivery_note\n            line_item_titles: Dictionary of line item titles customization\n                Can include keys like:\n                - description\n                - unit_price\n                - quantity\n                - discount\n            footer_details: Dictionary of footer details\n                Can include keys like:\n                - column_1\n                - column_2\n                - column_3\n            print_contact_details: Dictionary of print contact details settings\n                Can include keys like:\n                - business_name (boolean)\n                - contact_name (boolean)\n                - website (boolean)\n                - telephone (boolean)\n                - mobile (boolean)\n                - email_address (boolean)\n                - due_date (boolean)\n                - default_delivery_address (string)\n            print_statements: Dictionary of print statement settings\n                Can include keys like:\n                - days_overdue (boolean)\n                - table_of_balances (boolean)\n            customer_credit_days: Default customer credit days\n            vendor_credit_days: Default vendor credit days\n            customer_credit_terms: Customer credit terms (\"month_end_invoice\", \"date_from_invoice\", \"immediate_invoice\")\n            vendor_credit_terms: Vendor credit terms (\"month_end_payment\", \"date_from_payment\", \"immediate_payment\")\n            \n        Returns:\n            Dictionary containing the updated invoice settings\n        ", "parameters": {"properties": {"next_invoice_number": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Next Invoice Number"}, "next_credit_note_number": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Next Credit Note Number"}, "separate_invoice_credit_note_numbering": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Separate Invoice Credit Note Numbering"}, "sales_invoice_number_prefix": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Sales Invoice Number Prefix"}, "sales_credit_note_number_prefix": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Sales Credit Note Number Prefix"}, "invoice_terms_and_conditions": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Invoice Terms And Conditions"}, "default_note_on_invoice": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Default Note On Invoice"}, "default_note_on_credit_note": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Default Note On Credit Note"}, "default_note_on_estimate": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Default Note On Estimate"}, "default_note_on_quote": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Default Note On Quote"}, "next_quote_number": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Next Quote Number"}, "quote_number_prefix": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Quote Number Prefix"}, "estimate_number_prefix": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Estimate Number Prefix"}, "quote_default_days_to_expiry": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Quote Default Days To Expiry"}, "estimate_default_days_to_expiry": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Estimate Default Days To Expiry"}, "quote_terms_and_conditions": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Quote Terms And Conditions"}, "estimate_terms_and_conditions": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Estimate Terms And Conditions"}, "delivery_note_terms_and_conditions": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Delivery Note Terms And Conditions"}, "delivery_note_show_signature": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Delivery Note Show Signature"}, "delivery_note_show_picked": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Delivery Note Show Picked"}, "delivery_note_show_notes": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Delivery Note Show Notes"}, "delivery_note_show_contact_details": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Delivery Note Show Contact Details"}, "quick_entry_prefix": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Quick Entry Prefix"}, "late_payment_percentage": {"anyOf": [{"type": "number"}, {"type": "null"}], "default": null, "title": "Late Payment Percentage"}, "prompt_payment_percentage": {"anyOf": [{"type": "number"}, {"type": "null"}], "default": null, "title": "Prompt Payment Percentage"}, "show_auto_entrepreneur": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Show Auto Entrepreneur"}, "show_insurance": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Show Insurance"}, "insurance_area": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Insurance Area"}, "insurance_type": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Insurance Type"}, "insurance_text": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Insurance Text"}, "payment_bank_account_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Payment Bank Account Id"}, "sales_corrective_invoice_number_prefix": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Sales Corrective Invoice Number Prefix"}, "next_sales_corrective_invoice_number": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Next Sales Corrective Invoice Number"}, "document_headings": {"anyOf": [{"additionalProperties": {"type": "string"}, "type": "object"}, {"type": "null"}], "default": null, "title": "Document Headings"}, "line_item_titles": {"anyOf": [{"additionalProperties": {"type": "string"}, "type": "object"}, {"type": "null"}], "default": null, "title": "Line Item Titles"}, "footer_details": {"anyOf": [{"additionalProperties": {"type": "string"}, "type": "object"}, {"type": "null"}], "default": null, "title": "Footer Details"}, "print_contact_details": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "default": null, "title": "Print Contact Details"}, "print_statements": {"anyOf": [{"additionalProperties": {"type": "boolean"}, "type": "object"}, {"type": "null"}], "default": null, "title": "Print Statements"}, "customer_credit_days": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Customer Credit Days"}, "vendor_credit_days": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Vendor Credit Days"}, "customer_credit_terms": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Customer Credit Terms"}, "vendor_credit_terms": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Vendor Credit Terms"}}, "title": "update_invoice_settingsArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.864243+00:00"}, {"name": "get_analysis_types", "description": "Returns all Analysis Types.\n        \n        This tool retrieves analysis types from Sage Business Cloud Accounting.\n        Analysis types are used to categorize transactions for reporting purposes.\n        \n        Use this tool when you need to:\n        - List all analysis types\n        - Search for analysis types by name or code\n        - Filter analysis types by level (TRANSACTION or GROUP)\n        - Get analysis types for customized reporting\n        \n        Args:\n            analysis_type_level: Filter by analysis type level (\"TRANSACTION\" or \"GROUP\")\n            search: Search text to filter by item code or description\n            updated_or_created_since: Limit to items changed since given date\n            items_per_page: Number of items per page (default: 20, max: 200)\n            page: Page number to retrieve (default: 1)\n            attributes: Specify attributes to expose (comma-separated or 'all')\n            \n        Returns:\n            Dictionary containing list of analysis types and metadata\n        ", "parameters": {"properties": {"analysis_type_level": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Analysis Type Level"}, "search": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Search"}, "updated_or_created_since": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Updated Or Created Since"}, "items_per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 20, "title": "Items Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "title": "get_analysis_typesArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.864249+00:00"}, {"name": "get_analysis_type_by_id", "description": "Returns a specific Analysis Type.\n        \n        This tool retrieves detailed information about a specific analysis type\n        from Sage Business Cloud Accounting.\n        \n        Use this tool when you need to:\n        - Get detailed information about a specific analysis type\n        - View the categories associated with an analysis type\n        - Check which areas an analysis type is active in\n        \n        Args:\n            key: The analysis type ID\n            attributes: Specify attributes to expose (comma-separated or 'all')\n            \n        Returns:\n            Dictionary containing detailed analysis type information\n        ", "parameters": {"properties": {"key": {"title": "Key", "type": "string"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "required": ["key"], "title": "get_analysis_type_by_idArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.864255+00:00"}, {"name": "update_analysis_type", "description": "Updates a specific Analysis Type.\n        \n        This tool updates an existing analysis type in Sage Business Cloud Accounting.\n        \n        Use this tool when you need to:\n        - Modify the name of an analysis type\n        - Change which areas an analysis type is active in\n        - Update the analysis type level\n        - Add or modify analysis type categories\n        \n        Args:\n            key: The analysis type ID\n            name: The new name for the analysis type\n            active_areas: List of areas where this analysis type is active (e.g., [\"SALES\", \"EXPENSES\"])\n            analysis_type_level: Analysis type level information\n            analysis_type_categories: List of analysis type categories to associate with this type\n                Each category should be a dictionary with:\n                - code: The category code\n                - name: The category name\n                - combined_id: The combined ID (optional)\n                - analysis_type_id: The analysis type ID (optional)\n            \n        Returns:\n            Dictionary containing the updated analysis type\n        ", "parameters": {"properties": {"key": {"title": "Key", "type": "string"}, "name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Name"}, "active_areas": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Active Areas"}, "analysis_type_level": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Analysis Type Level"}, "analysis_type_categories": {"anyOf": [{"items": {"additionalProperties": true, "type": "object"}, "type": "array"}, {"type": "null"}], "default": null, "title": "Analysis Type Categories"}}, "required": ["key"], "title": "update_analysis_typeArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.864260+00:00"}, {"name": "get_analysis_type_categories", "description": "Returns all Analysis Type Categories.\n        \n        This tool retrieves analysis type categories from Sage Business Cloud Accounting.\n        Analysis type categories are specific classifications within an analysis type.\n        \n        Use this tool when you need to:\n        - List all analysis type categories\n        - Filter categories by analysis type level\n        - Get categories for reporting and analysis purposes\n        \n        Args:\n            analysis_type_level: Filter by analysis type level (\"TRANSACTION\" or \"GROUP\")\n            updated_or_created_since: Limit to items changed since given date\n            items_per_page: Number of items per page (default: 20, max: 200)\n            page: Page number to retrieve (default: 1)\n            attributes: Specify attributes to expose (comma-separated or 'all')\n            \n        Returns:\n            Dictionary containing list of analysis type categories and metadata\n        ", "parameters": {"properties": {"analysis_type_level": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Analysis Type Level"}, "updated_or_created_since": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Updated Or Created Since"}, "items_per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 20, "title": "Items Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "title": "get_analysis_type_categoriesArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.864266+00:00"}, {"name": "get_analysis_type_category_by_id", "description": "Returns a specific Analysis Type Category.\n        \n        This tool retrieves detailed information about a specific analysis type category\n        from Sage Business Cloud Accounting.\n        \n        Use this tool when you need to:\n        - Get detailed information about a specific analysis type category\n        - Check which analysis type a category belongs to\n        - View the code and name of a specific category\n        \n        Args:\n            key: The analysis type category ID\n            attributes: Specify attributes to expose (comma-separated or 'all')\n            \n        Returns:\n            Dictionary containing detailed analysis type category information\n        ", "parameters": {"properties": {"key": {"title": "Key", "type": "string"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "required": ["key"], "title": "get_analysis_type_category_by_idArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.864271+00:00"}, {"name": "create_analysis_type_category", "description": "Creates a new Analysis Type Category.\n        \n        This tool creates a new analysis type category in Sage Business Cloud Accounting.\n        Analysis type categories are specific classifications within an analysis type.\n        \n        Use this tool when you need to:\n        - Create a new category for an existing analysis type\n        - Add a new classification for reporting and analysis purposes\n        \n        Args:\n            code: The code for the new category (max 10 chars)\n            name: The name for the new category (max 50 chars)\n            analysis_type_id: The ID of the analysis type this category belongs to\n            combined_id: The combined ID (optional)\n            \n        Returns:\n            Dictionary containing the newly created analysis type category\n        ", "parameters": {"properties": {"code": {"title": "Code", "type": "string"}, "name": {"title": "Name", "type": "string"}, "analysis_type_id": {"title": "Analysis Type Id", "type": "string"}, "combined_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Combined Id"}}, "required": ["code", "name", "analysis_type_id"], "title": "create_analysis_type_categoryArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.864276+00:00"}, {"name": "update_analysis_type_category", "description": "Updates a specific Analysis Type Category.\n        \n        This tool updates an existing analysis type category in Sage Business Cloud Accounting.\n        \n        Use this tool when you need to:\n        - Modify the code or name of an analysis type category\n        - Change which analysis type a category belongs to\n        - Update the combined ID\n        \n        Args:\n            key: The analysis type category ID\n            code: The new code for the category (max 10 chars)\n            name: The new name for the category (max 50 chars)\n            analysis_type_id: The ID of the analysis type this category belongs to\n            combined_id: The combined ID\n            \n        Returns:\n            Dictionary containing the updated analysis type category\n        ", "parameters": {"properties": {"key": {"title": "Key", "type": "string"}, "code": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Code"}, "name": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Name"}, "analysis_type_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Analysis Type Id"}, "combined_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Combined Id"}}, "required": ["key"], "title": "update_analysis_type_categoryArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.864284+00:00"}, {"name": "delete_analysis_type_category", "description": "Deletes a specific Analysis Type Category.\n        \n        This tool deletes an existing analysis type category from Sage Business Cloud Accounting.\n        \n        Use this tool when you need to:\n        - Remove an unneeded analysis type category\n        - Clean up obsolete categories\n        \n        Args:\n            key: The analysis type category ID\n            \n        Returns:\n            Success confirmation or error details\n        ", "parameters": {"properties": {"key": {"title": "Key", "type": "string"}}, "required": ["key"], "title": "delete_analysis_type_categoryArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.864289+00:00"}, {"name": "get_business_activity_types", "description": "Returns all Business Activity Types.\n        \n        This tool retrieves business activity types from Sage Business Cloud Accounting.\n        Business activity types describe the commercial nature of a business and are\n        specific to French businesses.\n        \n        Use this tool when you need to:\n        - List all available business activity types\n        - Get business activity type IDs for updating business settings\n        - View available business activity classifications\n        \n        Note: This endpoint is only available for French businesses.\n        \n        Args:\n            items_per_page: Number of items per page (default: 20, max: 200)\n            page: Page number to retrieve (default: 1)\n            attributes: Specify attributes to expose (comma-separated or 'all')\n            \n        Returns:\n            Dictionary containing list of business activity types and metadata\n        ", "parameters": {"properties": {"items_per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 20, "title": "Items Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "title": "get_business_activity_typesArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.864295+00:00"}, {"name": "get_business_activity_type_by_id", "description": "Returns a specific Business Activity Type.\n        \n        This tool retrieves detailed information about a specific business activity type\n        from Sage Business Cloud Accounting.\n        \n        Use this tool when you need to:\n        - Get detailed information about a specific business activity type\n        - Verify a business activity type before updating business settings\n        \n        Note: This endpoint is only available for French businesses.\n        \n        Args:\n            key: The business activity type ID\n            attributes: Specify attributes to expose (comma-separated or 'all')\n            \n        Returns:\n            Dictionary containing detailed business activity type information\n        ", "parameters": {"properties": {"key": {"title": "Key", "type": "string"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "required": ["key"], "title": "get_business_activity_type_by_idArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.864300+00:00"}, {"name": "get_legal_form_types", "description": "Returns all Legal Form Types.\n        \n        This tool retrieves legal form types from Sage Business Cloud Accounting.\n        Legal form types describe the legal structure of a business and are\n        specific to French businesses.\n        \n        Use this tool when you need to:\n        - List all available legal form types\n        - Get legal form type IDs for updating business settings\n        - View available legal form classifications\n        \n        Note: This endpoint is only available for French businesses.\n        \n        Args:\n            items_per_page: Number of items per page (default: 20, max: 200)\n            page: Page number to retrieve (default: 1)\n            attributes: Specify attributes to expose (comma-separated or 'all')\n            \n        Returns:\n            Dictionary containing list of legal form types and metadata\n        ", "parameters": {"properties": {"items_per_page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 20, "title": "Items Per Page"}, "page": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 1, "title": "Page"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "title": "get_legal_form_typesArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.864306+00:00"}, {"name": "get_legal_form_type_by_id", "description": "Returns a specific Legal Form Type.\n        \n        This tool retrieves detailed information about a specific legal form type\n        from Sage Business Cloud Accounting.\n        \n        Use this tool when you need to:\n        - Get detailed information about a specific legal form type\n        - Verify a legal form type before updating business settings\n        \n        Note: This endpoint is only available for French businesses.\n        \n        Args:\n            key: The legal form type ID\n            attributes: Specify attributes to expose (comma-separated or 'all')\n            \n        Returns:\n            Dictionary containing detailed legal form type information\n        ", "parameters": {"properties": {"key": {"title": "Key", "type": "string"}, "attributes": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Attributes"}}, "required": ["key"], "title": "get_legal_form_type_by_idArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.864311+00:00"}, {"name": "get_cis_settings", "description": "Returns all CIS Settings.\n        \n        This tool retrieves Construction Industry Scheme (CIS) settings from \n        Sage Business Cloud Accounting. CIS settings are specific to UK businesses\n        and apply to construction industry work.\n        \n        CIS settings include:\n        - Unique Tax Reference (UTR)\n        - Contractor/subcontractor status\n        - Accounts Office Reference\n        - PAYE Reference\n        - CIS deduction tax rate\n        \n        Use this tool when you need to:\n        - View CIS configuration\n        - Check contractor/subcontractor status\n        - View tax reference numbers for CIS\n        \n        Note: This endpoint is only available for UK businesses.\n        \n        Args:\n            ctx: MCP context (automatically provided)\n            \n        Returns:\n            Dictionary containing CIS settings and configuration\n        ", "parameters": {"properties": {}, "title": "get_cis_settingsArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.864316+00:00"}, {"name": "update_cis_settings", "description": "Updates CIS Settings.\n        \n        This tool allows updating Construction Industry Scheme (CIS) settings in \n        Sage Business Cloud Accounting. CIS settings are specific to UK businesses\n        involved in the construction industry.\n        \n        Use this tool when you need to:\n        - Set or change the Unique Tax Reference (UTR)\n        - Update contractor or subcontractor status\n        - Modify Accounts Office Reference or PAYE Reference\n        - Change the CIS deduction tax rate\n        \n        Note: This endpoint is only available for UK businesses.\n        \n        Args:\n            unique_tax_reference: Unique Tax Reference number (10 digits)\n            contractor: Whether the business is a CIS contractor\n            subcontractor: Whether the business is a CIS subcontractor\n            accounts_office_reference: Accounts Office Reference (format: ###PX????????)\n            paye_reference: PAYE Reference (format: ###/X?#####)\n            tax_rate_id: The ID of the CIS deduction tax rate\n            \n        Returns:\n            Dictionary containing the updated CIS settings\n        ", "parameters": {"properties": {"unique_tax_reference": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Unique Tax Reference"}, "contractor": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Contractor"}, "subcontractor": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "default": null, "title": "Subcontractor"}, "accounts_office_reference": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Accounts Office Reference"}, "paye_reference": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Paye Reference"}, "tax_rate_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Tax Rate Id"}}, "title": "update_cis_settingsArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.864322+00:00"}, {"name": "get_datev_settings", "description": "Returns all DATEV Settings.\n        \n        This tool retrieves DATEV settings from Sage Business Cloud Accounting.\n        DATEV settings are specific to German businesses and are used for exporting\n        audit trail data in the DATEV format for German tax authorities.\n        \n        DATEV settings include:\n        - Tax consultant number\n        - Client number\n        - Next customer number\n        - Next supplier number\n        \n        Use this tool when you need to:\n        - View DATEV configuration\n        - Check tax consultant and client numbers\n        - View customer and supplier numbering settings\n        \n        Note: This endpoint is only available for German businesses.\n        \n        Args:\n            ctx: MCP context (automatically provided)\n            \n        Returns:\n            Dictionary containing DATEV settings and configuration\n        ", "parameters": {"properties": {}, "title": "get_datev_settingsArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.864327+00:00"}, {"name": "update_datev_settings", "description": "Updates DATEV Settings.\n        \n        This tool allows updating DATEV settings in Sage Business Cloud Accounting.\n        DATEV settings are specific to German businesses and are used for exporting\n        audit trail data in the DATEV format for German tax authorities.\n        \n        Use this tool when you need to:\n        - Set or change the tax consultant number\n        - Update the client number\n        - Modify the next customer and supplier numbers for DATEV exports\n        \n        Note: This endpoint is only available for German businesses.\n        \n        Args:\n            tax_consultant_number: Registration number of the accountant (1000-9999999)\n            client_number: The user's registration number (1-99999)\n            next_customer_number: The next unique customer number (10000-69999)\n            next_supplier_number: The next unique supplier number (70000-99999)\n            \n        Returns:\n            Dictionary containing the updated DATEV settings\n        ", "parameters": {"properties": {"tax_consultant_number": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Tax Consultant Number"}, "client_number": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Client Number"}, "next_customer_number": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Next Customer Number"}, "next_supplier_number": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Next Supplier Number"}}, "title": "update_datev_settingsArguments", "type": "object"}, "server_name": "Sage Business Cloud Accounting", "last_updated": "2025-06-10 13:07:41.864333+00:00"}]}