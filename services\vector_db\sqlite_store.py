"""
SQLite with sqlite-vec extension for vector operations
"""

import sqlite3
import json
import os
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import numpy as np
import structlog

from .base import VectorStore

logger = structlog.get_logger(__name__)


class SqliteVecStore(VectorStore):
    """SQLite with sqlite-vec extension for vector operations using single vec0 table."""
    
    def __init__(self, db_path: str = "data/ai_workspace_vectors.db"):
        self.db_path = db_path
        self._ensure_directory()
        self.conn: Optional[sqlite3.Connection] = None
        self.initialized = False
        
    def _ensure_directory(self):
        """Ensure the directory for the database exists."""
        dir_path = os.path.dirname(self.db_path)
        if dir_path and not os.path.exists(dir_path):
            os.makedirs(dir_path, exist_ok=True)
    
    async def initialize(self) -> None:
        """Initialize SQLite with vec extension using correct single vec0 table approach."""
        try:
            import sqlite_vec
            
            # Create connection
            self.conn = sqlite3.connect(self.db_path, check_same_thread=False)
            self.conn.row_factory = sqlite3.Row
            
            # Load sqlite-vec extension
            self.conn.enable_load_extension(True)
            sqlite_vec.load(self.conn)
            self.conn.enable_load_extension(False)
            
            # Create vec0 virtual table with correct syntax (based on sqlite-vec docs)
            self.conn.execute("""
                CREATE VIRTUAL TABLE IF NOT EXISTS tool_embeddings USING vec0(
                    embedding float[768]
                )
            """)
            
            # Create a regular table for metadata to work around vec0 limitations
            self.conn.execute("""
                CREATE TABLE IF NOT EXISTS tool_metadata (
                    id TEXT PRIMARY KEY,
                    rowid_hash INTEGER,
                    tool_name TEXT,
                    server_name TEXT,
                    description TEXT,
                    category TEXT,
                    parameters TEXT,
                    examples TEXT,
                    capabilities TEXT,
                    keywords TEXT,
                    execution_count INTEGER DEFAULT 0,
                    success_rate REAL DEFAULT 0.0,
                    avg_execution_time REAL DEFAULT 0.0,
                    last_used TEXT,
                    created_at TEXT,
                    updated_at TEXT
                )
            """)
            
            self.conn.commit()
            self.initialized = True
            logger.info("SQLite vector store initialized with correct vec0 architecture", path=self.db_path)
            
        except ImportError:
            logger.error("sqlite-vec not installed. Run: pip install sqlite-vec")
            raise
        except Exception as e:
            logger.error("Failed to initialize SQLite vector store", error=str(e))
            raise
    
    def _ensure_initialized(self):
        """Ensure the store is initialized."""
        if not self.initialized or not self.conn:
            raise RuntimeError("Vector store not initialized. Call initialize() first.")
    
    async def upsert(
        self,
        id: str,
        embedding: np.ndarray,
        metadata: Dict[str, Any]
    ) -> None:
        """Insert or update a vector with metadata using vec0 + metadata table."""
        self._ensure_initialized()
        
        try:
            # Ensure embedding is the right shape and type
            if embedding.shape != (768,):
                raise ValueError(f"Expected embedding shape (768,), got {embedding.shape}")
            
            # Convert embedding to JSON for sqlite-vec vec_f32() function
            embedding_json = '[' + ','.join(str(float(x)) for x in embedding) + ']'
            
            # Use a simple integer rowid based on string hash
            rowid = abs(hash(id)) % (2**31)
            self.conn.execute("""
                INSERT OR REPLACE INTO tool_embeddings (rowid, embedding) 
                VALUES (?, vec_f32(?))
            """, (rowid, embedding_json))
            
            # Extract metadata fields
            tool_name = metadata.get("tool_name", "")
            server_name = metadata.get("server", "")
            description = metadata.get("description", "")
            category = metadata.get("category", "general")
            parameters = json.dumps(metadata.get("parameters", {}))
            examples = json.dumps(metadata.get("examples", []))
            capabilities = json.dumps(metadata.get("capabilities", []))
            keywords = json.dumps(metadata.get("keywords", []))
            
            # Extract execution stats
            exec_stats = metadata.get("execution_stats", {})
            execution_count = exec_stats.get("count", 0)
            success_rate = exec_stats.get("success_rate", 0.0)
            avg_execution_time = exec_stats.get("avg_execution_time", 0.0)
            last_used = exec_stats.get("last_used")
            
            # Get current timestamp
            current_time = datetime.now().isoformat()
            
            # Insert/update metadata in regular table
            self.conn.execute("""
                INSERT OR REPLACE INTO tool_metadata (
                    id, rowid_hash, tool_name, server_name, description, category,
                    parameters, examples, capabilities, keywords,
                    execution_count, success_rate, avg_execution_time, last_used,
                    created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                id, rowid, tool_name, server_name, description, category,
                parameters, examples, capabilities, keywords,
                execution_count, success_rate, avg_execution_time, last_used,
                current_time, current_time
            ))
            
            self.conn.commit()
            
        except Exception as e:
            logger.error("Failed to upsert vector", id=id, error=str(e))
            self.conn.rollback()
            raise
    
    async def search(
        self,
        query_embedding: np.ndarray,
        k: int = 20,
        threshold: float = 0.7,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Tuple[str, float, Dict[str, Any]]]:
        """Fast vector similarity search using vec0 with metadata JOIN."""
        self._ensure_initialized()
        
        try:
            # Convert embedding to JSON for sqlite-vec vec_f32() function
            query_json = '[' + ','.join(str(float(x)) for x in query_embedding) + ']'
            
            # Use the MATCH syntax from sqlite-vec docs with literal LIMIT (required by sqlite-vec)
            # Based on sqlite-vec documentation, LIMIT must be literal, not parameter
            if filters and ("server" in filters or "category" in filters):
                # Build filter conditions for metadata
                filter_conditions = []
                filter_params = [query_json]
                if "server" in filters:
                    filter_conditions.append("m.server_name = ?")
                    filter_params.append(filters["server"])
                if "category" in filters:
                    filter_conditions.append("m.category = ?")
                    filter_params.append(filters["category"])
                
                where_clause = " AND " + " AND ".join(filter_conditions) if filter_conditions else ""
                
                # Use literal LIMIT as required by sqlite-vec
                query = f"""
                    SELECT m.id, m.tool_name, m.server_name, m.description, 
                           m.category, m.parameters, m.examples, m.capabilities, m.keywords,
                           m.execution_count, m.success_rate, m.avg_execution_time, m.last_used,
                           v.distance
                    FROM tool_embeddings v
                    JOIN tool_metadata m ON m.rowid_hash = v.rowid
                    WHERE v.embedding MATCH vec_f32(?){where_clause}
                    ORDER BY v.distance 
                    LIMIT {k}
                """
                params = filter_params
            else:
                # No filters - direct vector search with metadata JOIN and literal LIMIT
                query = f"""
                    SELECT m.id, m.tool_name, m.server_name, m.description, 
                           m.category, m.parameters, m.examples, m.capabilities, m.keywords,
                           m.execution_count, m.success_rate, m.avg_execution_time, m.last_used,
                           v.distance
                    FROM tool_embeddings v
                    JOIN tool_metadata m ON m.rowid_hash = v.rowid
                    WHERE v.embedding MATCH vec_f32(?)
                    ORDER BY v.distance 
                    LIMIT {k}
                """
                params = [query_json]
            
            cursor = self.conn.execute(query, params)
            results = []
            
            for row in cursor:
                # Convert distance to similarity
                similarity = 1 - row["distance"]
                
                # Only include results above threshold
                if similarity < threshold:
                    continue
                
                # Build metadata
                metadata = {
                    "tool_name": row["tool_name"],
                    "server": row["server_name"],
                    "description": row["description"],
                    "category": row["category"],
                    "parameters": json.loads(row["parameters"] or "{}"),
                    "examples": json.loads(row["examples"] or "[]"),
                    "capabilities": json.loads(row["capabilities"] or "[]"),
                    "keywords": json.loads(row["keywords"] or "[]"),
                    "execution_stats": {
                        "count": row["execution_count"],
                        "success_rate": row["success_rate"],
                        "avg_execution_time": row["avg_execution_time"],
                        "last_used": row["last_used"]
                    }
                }
                
                results.append((row["id"], similarity, metadata))
            
            return results
            
        except Exception as e:
            logger.error("Search failed", error=str(e))
            raise
    
    async def get(self, id: str) -> Optional[Tuple[np.ndarray, Dict[str, Any]]]:
        """Get a vector by ID from vec0 + metadata tables."""
        self._ensure_initialized()
        
        try:
            # Get metadata first
            cursor = self.conn.execute("""
                SELECT id, tool_name, server_name, description, category,
                       parameters, examples, capabilities, keywords,
                       execution_count, success_rate, avg_execution_time, last_used
                FROM tool_metadata 
                WHERE id = ?
            """, (id,))
            
            metadata_row = cursor.fetchone()
            if not metadata_row:
                return None
            
            # Get vector from vec0 table using stored rowid_hash
            rowid = abs(hash(id)) % (2**31)
            cursor = self.conn.execute("""
                SELECT embedding FROM tool_embeddings WHERE rowid = ?
            """, (rowid,))
            
            vector_row = cursor.fetchone()
            if not vector_row:
                return None
            
            # Convert vector from sqlite-vec format back to numpy array
            # The vector might be in sqlite-vec's internal format, so we use vec_to_json
            embedding_data = vector_row["embedding"]
            
            # If it's already JSON, parse it directly; otherwise it might be in binary format
            if isinstance(embedding_data, str):
                embedding = np.array(json.loads(embedding_data), dtype=np.float32)
            else:
                # For binary format, we'd need to use sqlite-vec functions to convert
                # For now, assume JSON format
                embedding = np.array(json.loads(str(embedding_data)), dtype=np.float32)
            
            # Build metadata
            metadata = {
                "tool_name": metadata_row["tool_name"],
                "server": metadata_row["server_name"],
                "description": metadata_row["description"],
                "category": metadata_row["category"],
                "parameters": json.loads(metadata_row["parameters"] or "{}"),
                "examples": json.loads(metadata_row["examples"] or "[]"),
                "capabilities": json.loads(metadata_row["capabilities"] or "[]"),
                "keywords": json.loads(metadata_row["keywords"] or "[]"),
                "execution_stats": {
                    "count": metadata_row["execution_count"],
                    "success_rate": metadata_row["success_rate"],
                    "avg_execution_time": metadata_row["avg_execution_time"],
                    "last_used": metadata_row["last_used"]
                }
            }
            
            return (embedding, metadata)
            
        except Exception as e:
            logger.error("Failed to get vector", id=id, error=str(e))
            raise
    
    async def delete(self, id: str) -> bool:
        """Delete a vector by ID from both tables."""
        self._ensure_initialized()
        
        try:
            # Delete from metadata table
            cursor1 = self.conn.execute("DELETE FROM tool_metadata WHERE id = ?", (id,))
            
            # Delete from vec0 table using computed rowid
            rowid = abs(hash(id)) % (2**31)
            cursor2 = self.conn.execute("DELETE FROM tool_embeddings WHERE rowid = ?", (rowid,))
            
            self.conn.commit()
            
            return cursor1.rowcount > 0 or cursor2.rowcount > 0
            
        except Exception as e:
            logger.error("Failed to delete vector", id=id, error=str(e))
            self.conn.rollback()
            raise
    
    async def update_metadata(
        self,
        id: str,
        metadata_updates: Dict[str, Any]
    ) -> bool:
        """Update metadata for a vector in metadata table."""
        self._ensure_initialized()
        
        try:
            # Build update query dynamically
            update_fields = []
            values = []
            
            # Handle execution stats specially
            if "execution_stats.count" in metadata_updates:
                if "$inc" in metadata_updates["execution_stats.count"]:
                    update_fields.append("execution_count = execution_count + ?")
                    values.append(metadata_updates["execution_stats.count"]["$inc"])
            
            if "execution_stats.success_rate" in metadata_updates:
                update_fields.append("success_rate = ?")
                values.append(metadata_updates["execution_stats.success_rate"])
            
            if "execution_stats.avg_execution_time" in metadata_updates:
                update_fields.append("avg_execution_time = ?")
                values.append(metadata_updates["execution_stats.avg_execution_time"])
            
            if "execution_stats.last_used" in metadata_updates:
                update_fields.append("last_used = ?")
                values.append(metadata_updates["execution_stats.last_used"])
            
            if not update_fields:
                return False
            
            # Always update the updated_at timestamp
            update_fields.append(f"updated_at = '{datetime.now().isoformat()}'")
            
            query = f"UPDATE tool_metadata SET {', '.join(update_fields)} WHERE id = ?"
            values.append(id)
            
            cursor = self.conn.execute(query, values)
            self.conn.commit()
            
            return cursor.rowcount > 0
            
        except Exception as e:
            logger.error("Failed to update metadata", id=id, error=str(e))
            self.conn.rollback()
            raise
    
    async def clear(self) -> None:
        """Clear all vectors from both tables."""
        self._ensure_initialized()
        
        try:
            self.conn.execute("DELETE FROM tool_embeddings")
            self.conn.execute("DELETE FROM tool_metadata")
            self.conn.commit()
            
        except Exception as e:
            logger.error("Failed to clear store", error=str(e))
            self.conn.rollback()
            raise
    
    async def count(self) -> int:
        """Get the total number of vectors in the store."""
        self._ensure_initialized()
        
        try:
            cursor = self.conn.execute("SELECT COUNT(*) as count FROM tool_metadata")
            return cursor.fetchone()["count"]
            
        except Exception as e:
            logger.error("Failed to count vectors", error=str(e))
            raise
    
    async def health_check(self) -> bool:
        """Check if the vector store is healthy and accessible."""
        try:
            if not self.initialized:
                await self.initialize()
            
            # Try a simple query
            cursor = self.conn.execute("SELECT 1")
            cursor.fetchone()
            
            return True
            
        except Exception as e:
            logger.error("Health check failed", error=str(e))
            return False
    
    def __del__(self):
        """Clean up connection on deletion."""
        if self.conn:
            self.conn.close()