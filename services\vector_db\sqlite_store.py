"""
SQLite with sqlite-vec extension for vector operations
"""

import sqlite3
import json
import os
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import numpy as np
import structlog

from .base import VectorStore

logger = structlog.get_logger(__name__)


class SqliteVecStore(VectorStore):
    """SQLite with sqlite-vec extension for vector operations using single vec0 table."""
    
    def __init__(self, db_path: str = "data/ai_workspace_vectors.db"):
        self.db_path = db_path
        self._ensure_directory()
        self.conn: Optional[sqlite3.Connection] = None
        self.initialized = False
        
    def _ensure_directory(self):
        """Ensure the directory for the database exists."""
        dir_path = os.path.dirname(self.db_path)
        if dir_path and not os.path.exists(dir_path):
            os.makedirs(dir_path, exist_ok=True)
    
    async def initialize(self) -> None:
        """Initialize SQLite with vec extension using single vec0 table with all metadata."""
        try:
            import sqlite_vec

            # Create connection
            self.conn = sqlite3.connect(self.db_path, check_same_thread=False)
            self.conn.row_factory = sqlite3.Row

            # Load sqlite-vec extension
            self.conn.enable_load_extension(True)
            sqlite_vec.load(self.conn)
            self.conn.enable_load_extension(False)

            # Create single vec0 virtual table with basic metadata columns
            # Note: sqlite-vec has limitations on column types, so we keep it simple
            self.conn.execute("""
                CREATE VIRTUAL TABLE IF NOT EXISTS tool_embeddings USING vec0(
                    id TEXT,
                    tool_name TEXT,
                    server_name TEXT,
                    description TEXT,
                    category TEXT,
                    type TEXT,
                    success INTEGER,
                    embedding FLOAT[768]
                )
            """)

            # Create a separate metadata table for complex fields that vec0 doesn't support
            self.conn.execute("""
                CREATE TABLE IF NOT EXISTS tool_metadata_extended (
                    id TEXT PRIMARY KEY,
                    parameters TEXT,
                    examples TEXT,
                    capabilities TEXT,
                    keywords TEXT,
                    execution_count INTEGER DEFAULT 0,
                    success_rate REAL DEFAULT 0.0,
                    avg_execution_time REAL DEFAULT 0.0,
                    last_used TEXT,
                    created_at TEXT,
                    updated_at TEXT
                )
            """)

            self.conn.commit()
            self.initialized = True
            logger.info("SQLite vector store initialized with native vec0 architecture", path=self.db_path)

        except ImportError:
            logger.error("sqlite-vec not installed. Run: pip install sqlite-vec")
            raise
        except Exception as e:
            logger.error("Failed to initialize SQLite vector store", error=str(e))
            raise
    
    def _ensure_initialized(self):
        """Ensure the store is initialized."""
        if not self.initialized or not self.conn:
            raise RuntimeError("Vector store not initialized. Call initialize() first.")
    
    async def upsert(
        self,
        id: str,
        embedding: np.ndarray,
        metadata: Dict[str, Any]
    ) -> None:
        """Insert or update a vector with metadata using single vec0 table."""
        self._ensure_initialized()

        try:
            # Ensure embedding is the right shape and type
            if embedding.shape != (768,):
                raise ValueError(f"Expected embedding shape (768,), got {embedding.shape}")

            # Convert embedding to JSON for sqlite-vec vec_f32() function
            embedding_json = '[' + ','.join(str(float(x)) for x in embedding) + ']'

            # Extract basic metadata fields for vec0 table
            tool_name = metadata.get("tool_name", "")
            server_name = metadata.get("server", "")
            description = metadata.get("description", "")
            category = metadata.get("category", "general")
            type_field = metadata.get("type", "tool")  # Default to "tool", can be "query_pattern"
            success = 1 if metadata.get("success", True) else 0  # Convert boolean to integer

            # Extract extended metadata fields for separate table
            parameters = json.dumps(metadata.get("parameters", {}))
            examples = json.dumps(metadata.get("examples", []))
            capabilities = json.dumps(metadata.get("capabilities", []))
            keywords = json.dumps(metadata.get("keywords", []))

            # Extract execution stats
            exec_stats = metadata.get("execution_stats", {})
            execution_count = exec_stats.get("count", 0)
            success_rate = exec_stats.get("success_rate", 0.0)
            avg_execution_time = exec_stats.get("avg_execution_time", 0.0)
            last_used = exec_stats.get("last_used")

            # Get current timestamp
            current_time = datetime.now().isoformat()

            # Insert/update basic fields in vec0 table
            self.conn.execute("""
                INSERT OR REPLACE INTO tool_embeddings (
                    id, tool_name, server_name, description, category, type, success, embedding
                ) VALUES (?, ?, ?, ?, ?, ?, ?, vec_f32(?))
            """, (
                id, tool_name, server_name, description, category, type_field, success, embedding_json
            ))

            # Insert/update extended metadata in separate table
            self.conn.execute("""
                INSERT OR REPLACE INTO tool_metadata_extended (
                    id, parameters, examples, capabilities, keywords,
                    execution_count, success_rate, avg_execution_time, last_used,
                    created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                id, parameters, examples, capabilities, keywords,
                execution_count, success_rate, avg_execution_time, last_used,
                current_time, current_time
            ))

            self.conn.commit()

        except Exception as e:
            logger.error("Failed to upsert vector", id=id, error=str(e))
            self.conn.rollback()
            raise
    
    async def search(
        self,
        query_embedding: np.ndarray,
        k: int = 20,
        threshold: float = 0.7,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Tuple[str, float, Dict[str, Any]]]:
        """Fast vector similarity search using native vec0 metadata filtering."""
        self._ensure_initialized()

        try:
            # Convert embedding to JSON for sqlite-vec vec_f32() function
            query_json = '[' + ','.join(str(float(x)) for x in query_embedding) + ']'

            # Build filter conditions using native sqlite-vec metadata filtering
            filter_conditions = []
            params = []

            if filters:
                if "server" in filters:
                    filter_conditions.append("server_name = ?")
                    params.append(filters["server"])
                if "category" in filters:
                    filter_conditions.append("category = ?")
                    params.append(filters["category"])
                if "type" in filters:
                    filter_conditions.append("type = ?")
                    params.append(filters["type"])
                if "success" in filters:
                    # Convert boolean to integer for comparison
                    success_val = 1 if filters["success"] else 0
                    filter_conditions.append("success = ?")
                    params.append(success_val)

            # Build WHERE clause
            where_clause = ""
            if filter_conditions:
                where_clause = " AND " + " AND ".join(filter_conditions)

            # Use native sqlite-vec MATCH syntax with metadata filtering
            # JOIN with extended metadata table for complete data
            query = f"""
                SELECT t.id, t.tool_name, t.server_name, t.description, t.category, t.type, t.success,
                       e.parameters, e.examples, e.capabilities, e.keywords,
                       e.execution_count, e.success_rate, e.avg_execution_time, e.last_used,
                       t.distance
                FROM tool_embeddings t
                LEFT JOIN tool_metadata_extended e ON t.id = e.id
                WHERE t.embedding MATCH vec_f32(?){where_clause}
                ORDER BY t.distance
                LIMIT {k}
            """

            # Execute query with embedding as first parameter
            all_params = [query_json] + params
            cursor = self.conn.execute(query, all_params)
            results = []

            for row in cursor:
                # Convert distance to similarity
                similarity = 1 - row["distance"]

                # Only include results above threshold
                if similarity < threshold:
                    continue

                # Build metadata
                metadata = {
                    "tool_name": row["tool_name"],
                    "server": row["server_name"],
                    "description": row["description"],
                    "category": row["category"],
                    "type": row["type"],
                    "success": bool(row["success"]),
                    "parameters": json.loads(row["parameters"] or "{}"),
                    "examples": json.loads(row["examples"] or "[]"),
                    "capabilities": json.loads(row["capabilities"] or "[]"),
                    "keywords": json.loads(row["keywords"] or "[]"),
                    "execution_stats": {
                        "count": row["execution_count"],
                        "success_rate": row["success_rate"],
                        "avg_execution_time": row["avg_execution_time"],
                        "last_used": row["last_used"]
                    }
                }

                results.append((row["id"], similarity, metadata))

            return results

        except Exception as e:
            logger.error("Search failed", error=str(e))
            raise
    
    async def get(self, id: str) -> Optional[Tuple[np.ndarray, Dict[str, Any]]]:
        """Get a vector by ID from single vec0 table."""
        self._ensure_initialized()

        try:
            # Get data from vec0 table and extended metadata table
            cursor = self.conn.execute("""
                SELECT t.id, t.tool_name, t.server_name, t.description, t.category, t.type, t.success,
                       e.parameters, e.examples, e.capabilities, e.keywords,
                       e.execution_count, e.success_rate, e.avg_execution_time, e.last_used,
                       vec_to_json(t.embedding) as embedding_json
                FROM tool_embeddings t
                LEFT JOIN tool_metadata_extended e ON t.id = e.id
                WHERE t.id = ?
            """, (id,))

            row = cursor.fetchone()
            if not row:
                return None

            # Convert vector from JSON back to numpy array
            embedding_json = row["embedding_json"]
            embedding = np.array(json.loads(embedding_json), dtype=np.float32)

            # Build metadata
            metadata = {
                "tool_name": row["tool_name"],
                "server": row["server_name"],
                "description": row["description"],
                "category": row["category"],
                "type": row["type"],
                "success": bool(row["success"]),
                "parameters": json.loads(row["parameters"] or "{}"),
                "examples": json.loads(row["examples"] or "[]"),
                "capabilities": json.loads(row["capabilities"] or "[]"),
                "keywords": json.loads(row["keywords"] or "[]"),
                "execution_stats": {
                    "count": row["execution_count"],
                    "success_rate": row["success_rate"],
                    "avg_execution_time": row["avg_execution_time"],
                    "last_used": row["last_used"]
                }
            }

            return (embedding, metadata)

        except Exception as e:
            logger.error("Failed to get vector", id=id, error=str(e))
            raise
    
    async def delete(self, id: str) -> bool:
        """Delete a vector by ID from single vec0 table."""
        self._ensure_initialized()

        try:
            # Delete from both tables
            cursor1 = self.conn.execute("DELETE FROM tool_embeddings WHERE id = ?", (id,))
            cursor2 = self.conn.execute("DELETE FROM tool_metadata_extended WHERE id = ?", (id,))

            self.conn.commit()

            return cursor1.rowcount > 0 or cursor2.rowcount > 0

        except Exception as e:
            logger.error("Failed to delete vector", id=id, error=str(e))
            self.conn.rollback()
            raise
    
    async def update_metadata(
        self,
        id: str,
        metadata_updates: Dict[str, Any]
    ) -> bool:
        """Update metadata for a vector in single vec0 table."""
        self._ensure_initialized()

        try:
            # Build update query dynamically
            update_fields = []
            values = []

            # Handle execution stats specially
            if "execution_stats.count" in metadata_updates:
                if "$inc" in metadata_updates["execution_stats.count"]:
                    update_fields.append("execution_count = execution_count + ?")
                    values.append(metadata_updates["execution_stats.count"]["$inc"])

            if "execution_stats.success_rate" in metadata_updates:
                update_fields.append("success_rate = ?")
                values.append(metadata_updates["execution_stats.success_rate"])

            if "execution_stats.avg_execution_time" in metadata_updates:
                update_fields.append("avg_execution_time = ?")
                values.append(metadata_updates["execution_stats.avg_execution_time"])

            if "execution_stats.last_used" in metadata_updates:
                update_fields.append("last_used = ?")
                values.append(metadata_updates["execution_stats.last_used"])

            if not update_fields:
                return False

            # Always update the updated_at timestamp
            update_fields.append(f"updated_at = '{datetime.now().isoformat()}'")

            query = f"UPDATE tool_metadata_extended SET {', '.join(update_fields)} WHERE id = ?"
            values.append(id)

            cursor = self.conn.execute(query, values)
            self.conn.commit()

            return cursor.rowcount > 0

        except Exception as e:
            logger.error("Failed to update metadata", id=id, error=str(e))
            self.conn.rollback()
            raise
    
    async def clear(self) -> None:
        """Clear all vectors from single vec0 table."""
        self._ensure_initialized()

        try:
            self.conn.execute("DELETE FROM tool_embeddings")
            self.conn.execute("DELETE FROM tool_metadata_extended")
            self.conn.commit()

        except Exception as e:
            logger.error("Failed to clear store", error=str(e))
            self.conn.rollback()
            raise

    async def count(self) -> int:
        """Get the total number of vectors in the store."""
        self._ensure_initialized()

        try:
            cursor = self.conn.execute("SELECT COUNT(*) as count FROM tool_embeddings")
            return cursor.fetchone()["count"]

        except Exception as e:
            logger.error("Failed to count vectors", error=str(e))
            raise
    
    async def health_check(self) -> bool:
        """Check if the vector store is healthy and accessible."""
        try:
            if not self.initialized:
                await self.initialize()
            
            # Try a simple query
            cursor = self.conn.execute("SELECT 1")
            cursor.fetchone()
            
            return True
            
        except Exception as e:
            logger.error("Health check failed", error=str(e))
            return False
    
    def __del__(self):
        """Clean up connection on deletion."""
        if self.conn:
            self.conn.close()