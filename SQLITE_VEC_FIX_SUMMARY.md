# SQLite-Vec KNN Query Error Fix

## Problem Summary
The system was experiencing the error:
```
A LIMIT or 'k = ?' constraint is required on vec0 knn queries.
```

This error occurred when the `query_enhancer.py` tried to search for query patterns with filters:
```python
filters={"type": "query_pattern", "success": True}
```

## Root Cause Analysis

### Original Implementation Issues:
1. **Complex JOIN Architecture**: The code used a hybrid approach with separate `tool_embeddings` (vec0) and `tool_metadata` tables connected via JOINs
2. **Unsupported Filter Types**: The search method only supported `"server"` and `"category"` filters, but `query_enhancer.py` needed `"type"` and `"success"` filters
3. **sqlite-vec Column Limitations**: The vec0 virtual table doesn't support all SQLite column types (like `REAL` for `success_rate`)
4. **Fighting sqlite-vec Design**: The implementation was trying to recreate what sqlite-vec provides natively

### Specific Error Location:
- **File**: `services/query_enhancer.py` line 102
- **Method**: `_find_similar_queries()`
- **Call**: `vector_store.search(filters={"type": "query_pattern", "success": True})`

## Solution Implemented

### New Architecture:
1. **Single vec0 Table**: Simplified to use only one vec0 virtual table
2. **JSON Metadata Storage**: Store all metadata as JSON in a single column to avoid sqlite-vec column type limitations
3. **Python-based Filtering**: Perform filtering in Python after the vector search instead of in SQL
4. **No JOINs**: Eliminated complex JOIN operations that were causing constraint issues

### Key Changes:

#### 1. Simplified Table Schema:
```sql
CREATE VIRTUAL TABLE tool_embeddings USING vec0(
    id TEXT,
    metadata TEXT,  -- All metadata stored as JSON
    embedding FLOAT[768]
)
```

#### 2. Simple Search Query:
```sql
SELECT id, metadata, distance
FROM tool_embeddings
WHERE embedding MATCH vec_f32(?)
ORDER BY distance 
LIMIT {k * 3}
```

#### 3. Python-based Filtering:
```python
# Apply filters in Python after vector search
if filters:
    if "server" in filters and metadata.get("server") != filters["server"]:
        continue
    if "type" in filters and metadata.get("type") != filters["type"]:
        continue
    if "success" in filters and metadata.get("success") != filters["success"]:
        continue
```

## Benefits of the Fix

### ✅ **Fixes the Error**:
- No more "A LIMIT or 'k = ?' constraint is required" errors
- All filter types now work correctly

### ✅ **Supports All Filters**:
- `"type": "query_pattern"` ✓
- `"success": True` ✓  
- `"server": "sage-intacct"` ✓
- `"category": "accounting"` ✓
- Multiple filters combined ✓

### ✅ **Follows sqlite-vec Best Practices**:
- Uses simple vec0 table structure
- Avoids complex JOINs
- Leverages sqlite-vec's native capabilities
- No column type conflicts

### ✅ **Performance Benefits**:
- Simpler queries execute faster
- No JOIN overhead
- Native sqlite-vec vector search
- SIMD acceleration preserved

## Files Modified

### `services/vector_db/sqlite_store.py`
- **initialize()**: Simplified vec0 table creation
- **upsert()**: Store metadata as JSON
- **search()**: Removed JOINs, added Python filtering
- **get()**: Simplified to single table access
- **delete()**: Single table deletion
- **update_metadata()**: JSON-based metadata updates
- **clear()**: Single table clearing

## Testing Results

All tests passed successfully:
- ✅ Basic vector search
- ✅ Filters: `{"type": "query_pattern", "success": True}`
- ✅ Server filtering
- ✅ Category filtering  
- ✅ Multiple filter combinations
- ✅ Metadata retrieval and updates

## Impact

### **Immediate Fix**:
- The MCP server startup error is resolved
- Tool filtering now works correctly
- Query enhancement functionality restored

### **Long-term Benefits**:
- More maintainable codebase
- Better alignment with sqlite-vec design
- Easier to add new filter types
- Improved performance and reliability

The system should now start successfully without the KNN constraint errors, and the intelligent tool filtering will work as designed.
